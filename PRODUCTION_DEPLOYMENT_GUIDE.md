# 🚀 Production Deployment Guide - Render + Netlify

## 📋 **Overview**
This guide will help you deploy your IRCTC Auto Booking system to production:
- **Backend**: Render (Free tier available)
- **Frontend**: Netlify (Free tier available)
- **Database**: MongoDB Atlas (Free tier available)

## 🎯 **Pre-Deployment Checklist**

### **1. Get Production Services Ready**
- [ ] Create MongoDB Atlas account and cluster
- [ ] Create Render account
- [ ] Create Netlify account
- [ ] Get production Google OAuth credentials
- [ ] Get production Razorpay credentials

### **2. Prepare Your Code**
- [ ] Push your code to GitHub repository
- [ ] Ensure all environment files are configured
- [ ] Test locally one more time

## 🗄️ **Step 1: Setup MongoDB Atlas**

1. **Create MongoDB Atlas Account**
   - Go to [MongoDB Atlas](https://www.mongodb.com/atlas)
   - Sign up for free account

2. **Create Cluster**
   - Choose "Build a Database" → "Free" tier
   - Select cloud provider and region
   - Create cluster (takes 1-3 minutes)

3. **Setup Database Access**
   - Go to "Database Access" → "Add New Database User"
   - Create username/password (save these!)
   - Grant "Read and write to any database" permissions

4. **Setup Network Access**
   - Go to "Network Access" → "Add IP Address"
   - Click "Allow Access from Anywhere" (0.0.0.0/0)
   - Confirm

5. **Get Connection String**
   - Go to "Database" → "Connect" → "Connect your application"
   - Copy the connection string
   - Replace `<password>` with your database user password
   - Save this as your `MONGODB_URI`

## 🖥️ **Step 2: Deploy Backend to Render**

1. **Create Render Account**
   - Go to [Render](https://render.com)
   - Sign up with GitHub

2. **Create Web Service**
   - Click "New" → "Web Service"
   - Connect your GitHub repository
   - Select the `irctc-backend` folder (or root if backend is in root)

3. **Configure Service**
   ```
   Name: irctc-backend
   Environment: Node
   Build Command: npm install
   Start Command: npm start
   ```

4. **Set Environment Variables**
   In Render dashboard, add these environment variables:
   ```
   NODE_ENV=production
   PORT=10000
   MONGODB_URI=mongodb+srv://username:<EMAIL>/irctc-auto
   JWT_SECRET=your-super-long-random-secret-key-here
   GOOGLE_CLIENT_ID=your-production-google-client-id
   GOOGLE_CLIENT_SECRET=your-production-google-client-secret
   RAZORPAY_KEY_ID=your-production-razorpay-key-id
   RAZORPAY_KEY_SECRET=your-production-razorpay-key-secret
   FRONTEND_URL_PROD=https://your-netlify-app.netlify.app
   ADMIN_EMAIL=<EMAIL>
   ADMIN_PASSWORD=your-secure-admin-password
   ```

5. **Deploy**
   - Click "Create Web Service"
   - Wait for deployment (5-10 minutes)
   - Note your Render URL: `https://your-app-name.onrender.com`

## 🌐 **Step 3: Deploy Frontend to Netlify**

1. **Update Production Config**
   Edit `irctc-website/.env.production`:
   ```env
   REACT_APP_API_URL=https://your-render-app.onrender.com/api
   REACT_APP_GOOGLE_CLIENT_ID=your-production-google-client-id
   REACT_APP_FACEBOOK_APP_ID=your-production-facebook-app-id
   REACT_APP_RAZORPAY_KEY_ID=your-production-razorpay-key-id
   REACT_APP_ENVIRONMENT=production
   ```

2. **Create Netlify Site**
   - Go to [Netlify](https://netlify.com)
   - Sign up with GitHub
   - Click "New site from Git"
   - Choose your repository
   - Set build settings:
     ```
     Base directory: irctc-website
     Build command: npm run build
     Publish directory: irctc-website/build
     ```

3. **Set Environment Variables**
   In Netlify dashboard → Site settings → Environment variables:
   ```
   REACT_APP_API_URL=https://your-render-app.onrender.com/api
   REACT_APP_GOOGLE_CLIENT_ID=your-production-google-client-id
   REACT_APP_FACEBOOK_APP_ID=your-production-facebook-app-id
   REACT_APP_RAZORPAY_KEY_ID=your-production-razorpay-key-id
   REACT_APP_ENVIRONMENT=production
   ```

4. **Deploy**
   - Click "Deploy site"
   - Wait for build (3-5 minutes)
   - Note your Netlify URL: `https://your-app-name.netlify.app`

## 🔌 **Step 4: Update Chrome Extension**

1. **Update Production URLs**
   Edit `irctc-tatkal/build-config.js`:
   ```javascript
   production: {
       API_BASE_URL: 'https://your-render-app.onrender.com/api',
       PAYMENT_URL: 'https://your-netlify-app.netlify.app/payment',
       ENVIRONMENT: 'production'
   }
   ```

2. **Build Production Extension**
   ```bash
   cd irctc-tatkal
   npm run build:prod
   npm run package:prod
   ```

3. **Upload to Chrome Web Store**
   - Upload `irctc-extension-prod.zip` to Chrome Web Store
   - Or load unpacked for testing

## 🔧 **Step 5: Update OAuth Settings**

### **Google OAuth**
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project
3. Go to "Credentials" → Edit OAuth 2.0 Client
4. Add authorized origins:
   ```
   https://your-netlify-app.netlify.app
   https://your-render-app.onrender.com
   ```

### **Facebook OAuth**
1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Select your app
3. Go to Facebook Login → Settings
4. Add Valid OAuth Redirect URIs:
   ```
   https://your-netlify-app.netlify.app
   ```

## ✅ **Step 6: Test Production Deployment**

1. **Test Backend**
   - Visit: `https://your-render-app.onrender.com/health`
   - Should return: `{"status":"ok","message":"Server is running"}`

2. **Test Frontend**
   - Visit: `https://your-netlify-app.netlify.app`
   - Should load the landing page

3. **Test Integration**
   - Try user registration/login
   - Test payment flow
   - Test Chrome extension

## 🔄 **Step 7: Update CORS Settings**

Update `irctc-backend/.env` on Render:
```env
FRONTEND_URL_PROD=https://your-actual-netlify-url.netlify.app
```

## 📝 **Quick Reference URLs**

After deployment, update these in your documentation:

### **Production URLs**
- **Frontend**: `https://your-netlify-app.netlify.app`
- **Backend**: `https://your-render-app.onrender.com`
- **API**: `https://your-render-app.onrender.com/api`
- **Admin**: `https://your-render-app.onrender.com/dashboard`

### **Development URLs**
- **Frontend**: `http://localhost:3001`
- **Backend**: `http://localhost:3000`
- **API**: `http://localhost:3000/api`

## 🚨 **Important Notes**

1. **Free Tier Limitations**
   - Render: Service sleeps after 15 minutes of inactivity
   - Netlify: 100GB bandwidth/month
   - MongoDB Atlas: 512MB storage

2. **Security**
   - Use strong JWT secrets in production
   - Enable HTTPS only
   - Set secure admin passwords

3. **Monitoring**
   - Check Render logs for backend issues
   - Check Netlify deploy logs for frontend issues
   - Monitor MongoDB Atlas for database issues

## 🎉 **You're Live!**

Your IRCTC Auto Booking system is now live in production! 🚀

**Next Steps:**
- Test all functionality thoroughly
- Set up monitoring and alerts
- Consider upgrading to paid tiers for better performance
- Implement backup strategies
