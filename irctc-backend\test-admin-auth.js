require('dotenv').config();
const jwt = require('jsonwebtoken');
const mongoose = require('mongoose');
const Admin = require('./models/Admin');
const connectDB = require('./config/db');

// Connect to MongoDB
connectDB();

const testAdminAuth = async () => {
    try {
        // Find the admin user
        const admin = await Admin.findOne({ email: '<EMAIL>' });
        
        if (!admin) {
            console.log('Admin not found. Please run create-admin.js first.');
            return;
        }
        
        console.log('Admin found:', {
            id: admin._id,
            name: admin.name,
            email: admin.email
        });
        
        // Create a token for the admin
        const token = jwt.sign(
            { id: admin._id },
            process.env.JWT_SECRET || 'your-secret-key-here',
            { expiresIn: '1d' }
        );
        
        console.log('Admin token created:', token);
        
        // Verify the token
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key-here');
        console.log('Decoded token:', decoded);
        
        // Test the auth middleware logic
        const mockReq = {
            header: () => `Bearer ${token}`
        };
        
        const mockRes = {
            status: (code) => ({
                json: (data) => console.log(`Response: ${code}`, data)
            })
        };
        
        const mockNext = () => console.log('Next middleware called');
        
        // Simulate the auth middleware
        const auth = require('./middleware/auth');
        await auth(mockReq, mockRes, mockNext);
        
        console.log('Request after auth middleware:', {
            admin: mockReq.admin ? 'Admin found' : 'No admin',
            isAdmin: mockReq.isAdmin,
            user: mockReq.user ? 'User found' : 'No user'
        });
        
    } catch (error) {
        console.error('Error testing admin auth:', error);
    } finally {
        // Close the connection
        mongoose.connection.close();
        console.log('Database connection closed');
    }
};

testAdminAuth();
