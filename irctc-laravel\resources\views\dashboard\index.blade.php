@extends('layouts.app')

@section('title', 'Dashboard - IRCTC Auto Booking')

@section('content')
<div class="container py-4">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 bg-primary text-white">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-2">Welcome back, {{ $user->name }}! 👋</h2>
                            <p class="mb-0 opacity-75">Manage your tickets and credits from your dashboard</p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            @if($user->picture)
                                <img src="{{ $user->picture }}" alt="Profile" class="rounded-circle" width="80" height="80">
                            @else
                                <div class="bg-white text-primary rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                                    <i class="fas fa-user fa-2x"></i>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-ticket-alt fa-lg"></i>
                    </div>
                    <h3 class="text-primary mb-1">{{ $user->available_tickets }}</h3>
                    <p class="text-muted mb-0">Available Credits</p>
                    <a href="{{ route('buy-credits') }}" class="btn btn-sm btn-outline-primary mt-2">
                        <i class="fas fa-plus me-1"></i>Buy More
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-train fa-lg"></i>
                    </div>
                    <h3 class="text-success mb-1">{{ $recentTickets->count() }}</h3>
                    <p class="text-muted mb-0">Total Bookings</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="bg-info text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-credit-card fa-lg"></i>
                    </div>
                    <h3 class="text-info mb-1">{{ $recentTransactions->count() }}</h3>
                    <p class="text-muted mb-0">Transactions</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="bg-warning text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-calendar fa-lg"></i>
                    </div>
                    <h3 class="text-warning mb-1">{{ $user->created_at->diffInDays() }}</h3>
                    <p class="text-muted mb-0">Days Active</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <a href="{{ route('buy-credits') }}" class="btn btn-primary w-100 py-3">
                                <i class="fas fa-shopping-cart d-block mb-2"></i>
                                Buy Credits
                            </a>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-success w-100 py-3" onclick="openExtension()">
                                <i class="fas fa-plus d-block mb-2"></i>
                                Book Ticket
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-info w-100 py-3" onclick="copyApiKey()">
                                <i class="fas fa-key d-block mb-2"></i>
                                Copy API Key
                            </button>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('support') }}" class="btn btn-outline-secondary w-100 py-3">
                                <i class="fas fa-headset d-block mb-2"></i>
                                Get Support
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Tickets -->
        <div class="col-lg-8 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-train me-2"></i>Recent Bookings</h5>
                    <small class="text-muted">Last 5 bookings</small>
                </div>
                <div class="card-body">
                    @if($recentTickets->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Route</th>
                                        <th>Date</th>
                                        <th>PNR</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentTickets as $ticket)
                                        <tr>
                                            <td>
                                                <strong>{{ $ticket->from_station }}</strong> → <strong>{{ $ticket->to_station }}</strong>
                                                @if($ticket->train_number)
                                                    <br><small class="text-muted">{{ $ticket->train_number }}</small>
                                                @endif
                                            </td>
                                            <td>{{ $ticket->journey_date->format('d M Y') }}</td>
                                            <td><code>{{ $ticket->pnr }}</code></td>
                                            <td>
                                                @switch($ticket->status)
                                                    @case('booked')
                                                        <span class="badge bg-primary">Booked</span>
                                                        @break
                                                    @case('confirmed')
                                                        <span class="badge bg-success">Confirmed</span>
                                                        @break
                                                    @case('cancelled')
                                                        <span class="badge bg-danger">Cancelled</span>
                                                        @break
                                                    @default
                                                        <span class="badge bg-secondary">{{ ucfirst($ticket->status) }}</span>
                                                @endswitch
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-train fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No bookings yet</h6>
                            <p class="text-muted mb-3">Start booking tickets with our Chrome extension</p>
                            <button class="btn btn-primary" onclick="openExtension()">
                                <i class="fas fa-plus me-2"></i>Book Your First Ticket
                            </button>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Recent Transactions -->
        <div class="col-lg-4 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>Recent Transactions</h5>
                </div>
                <div class="card-body">
                    @if($recentTransactions->count() > 0)
                        @foreach($recentTransactions as $transaction)
                            <div class="d-flex justify-content-between align-items-center mb-3 pb-3 border-bottom">
                                <div>
                                    <h6 class="mb-1">
                                        @if($transaction->type === 'credit_purchase')
                                            <i class="fas fa-shopping-cart text-primary me-2"></i>Credit Purchase
                                        @elseif($transaction->type === 'bonus')
                                            <i class="fas fa-gift text-success me-2"></i>Bonus Credits
                                        @else
                                            <i class="fas fa-exchange-alt text-info me-2"></i>{{ ucfirst($transaction->type) }}
                                        @endif
                                    </h6>
                                    <small class="text-muted">{{ $transaction->created_at->diffForHumans() }}</small>
                                </div>
                                <div class="text-end">
                                    <div class="fw-bold">+{{ $transaction->credits }} credits</div>
                                    <small class="text-muted">₹{{ number_format($transaction->amount, 2) }}</small>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-credit-card fa-2x text-muted mb-3"></i>
                            <h6 class="text-muted">No transactions yet</h6>
                            <p class="text-muted mb-3">Purchase credits to get started</p>
                            <a href="{{ route('buy-credits') }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-shopping-cart me-2"></i>Buy Credits
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Extension Download Section -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm bg-light">
                <div class="card-body text-center py-4">
                    <h5 class="mb-3"><i class="fab fa-chrome me-2"></i>Chrome Extension</h5>
                    <p class="text-muted mb-3">Download our Chrome extension to start booking tickets automatically</p>
                    <button class="btn btn-primary me-2" onclick="downloadExtension()">
                        <i class="fas fa-download me-2"></i>Download Extension
                    </button>
                    <button class="btn btn-outline-primary" onclick="showInstructions()">
                        <i class="fas fa-question-circle me-2"></i>Installation Guide
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden API Key for copying -->
<input type="hidden" id="apiKey" value="{{ $user->api_key }}">
@endsection

@push('scripts')
<script>
function copyApiKey() {
    const apiKey = document.getElementById('apiKey').value;
    navigator.clipboard.writeText(apiKey).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
        toast.style.zIndex = '9999';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check me-2"></i>API Key copied to clipboard!
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        document.body.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // Remove toast after it's hidden
        toast.addEventListener('hidden.bs.toast', function() {
            document.body.removeChild(toast);
        });
    });
}

function openExtension() {
    alert('Please install our Chrome extension first. Click "Download Extension" button below.');
}

function downloadExtension() {
    alert('Extension download will be available soon. Please check back later.');
}

function showInstructions() {
    alert('Installation instructions will be provided with the extension download.');
}
</script>
@endpush
