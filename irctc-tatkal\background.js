// Global state to track automation status
let automationStatus = {
    state: 'running', // 'running' or 'paused'
    lastUpdated: Date.now()
};

// Helper function to broadcast automation status to all content scripts
function broadcastAutomationStatus() {
    chrome.tabs.query({url: "https://www.irctc.co.in/*"}, function(tabs) {
        tabs.forEach(tab => {
            chrome.tabs.sendMessage(tab.id, {action: 'updateAutomationStatus', status: automationStatus});
        });
    });
}

// Handle various extension messages
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'openPopup') {
        chrome.action.openPopup();
    }
    else if (request.action === 'pauseAutomation') {
        automationStatus.state = 'paused';
        automationStatus.lastUpdated = Date.now();
        sendResponse({ status: 'success', currentState: automationStatus.state });
        // Broadcast to all content scripts
        broadcastAutomationStatus();
    }
    else if (request.action === 'resumeAutomation') {
        automationStatus.state = 'running';
        automationStatus.lastUpdated = Date.now();
        sendResponse({ status: 'success', currentState: automationStatus.state });
        // Broadcast to all content scripts
        broadcastAutomationStatus();
    }
    else if (request.action === 'getAutomationStatus') {
        sendResponse({ status: 'success', automationStatus: automationStatus });
    }

    return true; // Required for async sendResponse
});

// Handle installation or update
chrome.runtime.onInstalled.addListener((details) => {
    if (details.reason === 'install') {
        // Open welcome page or dashboard on fresh install
        chrome.tabs.create({
            url: chrome.runtime.getURL('popup.html'),
            active: true
        });
    }
});

// Handle extension icon click when already logged in
chrome.action.onClicked.addListener((tab) => {
    chrome.storage.local.get(['isLoggedIn'], function(result) {
        if (result.isLoggedIn) {
            // If logged in, open the controls popup
            chrome.tabs.create({
                url: chrome.runtime.getURL('popup-controls.html'),
                active: true
            });
        } else {
            // If not logged in, open the regular popup
            chrome.action.openPopup();
        }
    });
});


