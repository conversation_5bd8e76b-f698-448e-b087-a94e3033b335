const mongoose = require('mongoose');

// Define the ticket schema
const ticketSchema = new mongoose.Schema({
    fromStation: {
        type: String,
        required: true
    },
    toStation: {
        type: String,
        required: true
    },
    journeyDate: {
        type: Date,
        required: true
    },
    class: {
        type: String,
        enum: ['1A', '2A', '3A', 'SL', 'CC', 'EC'],
        required: true
    },
    status: {
        type: String,
        enum: ['booked', 'confirmed', 'cancelled', 'completed'],
        default: 'booked'
    },
    pnr: String,
    bookingDate: {
        type: Date,
        default: Date.now
    }
});

const userSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true
    },
    email: {
        type: String,
        required: true,
        unique: true
    },
    googleId: String,
    facebookId: String,
    picture: String,
    apiKey: String,
    availableTickets: {
        type: Number,
        default: 1  // New users get 1 ticket by default
    },
    bookedTickets: [ticketSchema],
    status: {
        type: String,
        enum: ['active', 'suspended', 'deleted'],
        default: 'active'
    },
    lastLogin: Date,
    createdAt: {
        type: Date,
        default: Date.now
    }
});

module.exports = mongoose.model('User', userSchema);
