<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Razorpay\Api\Api;

class DashboardController extends Controller
{
    protected $razorpay;

    public function __construct()
    {
        $this->razorpay = new Api(
            config('services.razorpay.key_id'),
            config('services.razorpay.key_secret')
        );
    }

    /**
     * Show user dashboard
     */
    public function index(Request $request)
    {
        $user = $request->user();

        // Get user's recent tickets
        $recentTickets = $user->tickets()
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get user's recent transactions
        $recentTransactions = $user->transactions()
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        return view('dashboard.index', compact('user', 'recentTickets', 'recentTransactions'));
    }

    /**
     * Show buy credits page
     */
    public function buyCredits(Request $request)
    {
        $user = $request->user();

        // Credit packages
        $packages = [
            [
                'name' => 'Starter Pack',
                'credits' => 5,
                'price' => 250,
                'popular' => false,
            ],
            [
                'name' => 'Popular Pack',
                'credits' => 10,
                'price' => 450,
                'popular' => true,
                'savings' => 50,
            ],
            [
                'name' => 'Premium Pack',
                'credits' => 25,
                'price' => 1000,
                'popular' => false,
                'savings' => 250,
            ],
            [
                'name' => 'Enterprise Pack',
                'credits' => 50,
                'price' => 1800,
                'popular' => false,
                'savings' => 700,
            ],
        ];

        return view('dashboard.buy-credits', compact('user', 'packages'));
    }

    /**
     * Create payment order
     */
    public function createPaymentOrder(Request $request)
    {
        try {
            $request->validate([
                'amount' => 'required|integer|min:100',
                'credits' => 'required|integer|min:1',
            ]);

            $user = $request->user();

            // Create Razorpay order
            $orderData = [
                'receipt' => 'receipt_' . $user->id . '_' . time(),
                'amount' => $request->amount * 100, // Convert to paise
                'currency' => 'INR',
                'notes' => [
                    'user_id' => $user->id,
                    'credits' => $request->credits,
                    'order_type' => 'credit_purchase'
                ]
            ];

            $order = $this->razorpay->order->create($orderData);

            // Save transaction to database
            $transaction = Transaction::create([
                'user_id' => $user->id,
                'order_id' => $order['id'],
                'amount' => $request->amount,
                'currency' => 'INR',
                'credits' => $request->credits,
                'status' => 'created',
                'type' => 'credit_purchase'
            ]);

            return response()->json([
                'success' => true,
                'order_id' => $order['id'],
                'amount' => $order['amount'],
                'currency' => $order['currency'],
                'key' => config('services.razorpay.key_id'),
                'name' => config('app.name'),
                'description' => $request->credits . ' Credits Purchase',
                'prefill' => [
                    'name' => $user->name,
                    'email' => $user->email,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create order',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Verify payment
     */
    public function verifyPayment(Request $request)
    {
        try {
            $request->validate([
                'razorpay_order_id' => 'required|string',
                'razorpay_payment_id' => 'required|string',
                'razorpay_signature' => 'required|string',
            ]);

            $user = $request->user();

            // Verify signature
            $attributes = [
                'razorpay_order_id' => $request->razorpay_order_id,
                'razorpay_payment_id' => $request->razorpay_payment_id,
                'razorpay_signature' => $request->razorpay_signature
            ];

            $this->razorpay->utility->verifyPaymentSignature($attributes);

            // Find transaction
            $transaction = Transaction::where('order_id', $request->razorpay_order_id)
                                   ->where('user_id', $user->id)
                                   ->first();

            if (!$transaction) {
                return response()->json([
                    'success' => false,
                    'message' => 'Transaction not found'
                ], 404);
            }

            // Update transaction
            $transaction->update([
                'payment_id' => $request->razorpay_payment_id,
                'signature' => $request->razorpay_signature,
                'status' => 'completed',
                'description' => "Purchase of {$transaction->credits} credits"
            ]);

            // Add credits to user account
            $user->increment('available_tickets', $transaction->credits);

            return response()->json([
                'success' => true,
                'message' => 'Payment verified successfully',
                'credits' => $transaction->credits,
                'new_balance' => $user->fresh()->available_tickets,
                'redirect' => route('dashboard')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Payment verification failed',
                'error' => $e->getMessage()
            ], 400);
        }
    }
}
