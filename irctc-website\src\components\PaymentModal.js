import React, { useState } from 'react';
import { paymentAPI, getCurrentUser, handleApiError } from '../services/api';
import '../styles/PaymentModal.css';

const PaymentModal = ({ isOpen, onClose, creditPackage, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const user = getCurrentUser();

  const handlePayment = async () => {
    try {
      setLoading(true);
      setError('');

      // Create order on backend
      const orderResponse = await paymentAPI.createOrder(
        creditPackage.price * 100, // Razorpay expects amount in paise
        'INR'
      );

      const { orderId, amount, currency } = orderResponse.data;

      // Razorpay configuration
      const options = {
        key: process.env.REACT_APP_RAZORPAY_KEY_ID || 'rzp_test_your_key_id',
        amount: amount,
        currency: currency,
        name: 'IRCTC Tatkal Extension',
        description: `${creditPackage.credits} Credits Purchase`,
        order_id: orderId,
        handler: async function (response) {
          try {
            // Verify payment on backend
            const verifyResponse = await paymentAPI.verifyPayment({
              razorpay_order_id: response.razorpay_order_id,
              razorpay_payment_id: response.razorpay_payment_id,
              razorpay_signature: response.razorpay_signature,
              credits: creditPackage.credits,
              amount: creditPackage.price
            });

            if (verifyResponse.data.success) {
              onSuccess(creditPackage.credits);
              onClose();
            } else {
              setError('Payment verification failed. Please contact support.');
            }
          } catch (error) {
            console.error('Payment verification error:', error);
            setError('Payment verification failed. Please contact support.');
          }
        },
        prefill: {
          name: user?.name || '',
          email: user?.email || '',
          contact: user?.phone || ''
        },
        notes: {
          credits: creditPackage.credits,
          user_id: user?.id || user?._id
        },
        theme: {
          color: '#667eea'
        },
        modal: {
          ondismiss: function() {
            setLoading(false);
          }
        }
      };

      // Load Razorpay script if not already loaded
      if (!window.Razorpay) {
        const script = document.createElement('script');
        script.src = 'https://checkout.razorpay.com/v1/checkout.js';
        script.onload = () => {
          const rzp = new window.Razorpay(options);
          rzp.open();
        };
        script.onerror = () => {
          setError('Failed to load payment gateway. Please try again.');
          setLoading(false);
        };
        document.body.appendChild(script);
      } else {
        const rzp = new window.Razorpay(options);
        rzp.open();
      }

    } catch (error) {
      console.error('Payment initiation error:', error);
      setError(handleApiError(error));
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="payment-modal-overlay">
      <div className="payment-modal">
        <div className="payment-header">
          <h2>Complete Your Purchase</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        <div className="payment-content">
          <div className="package-summary">
            <h3>{creditPackage.name}</h3>
            <div className="package-details">
              <div className="detail-row">
                <span>Credits:</span>
                <span>{creditPackage.credits}</span>
              </div>
              <div className="detail-row">
                <span>Price per credit:</span>
                <span>₹{(creditPackage.price / creditPackage.credits).toFixed(2)}</span>
              </div>
              <div className="detail-row total">
                <span>Total Amount:</span>
                <span>₹{creditPackage.price}</span>
              </div>
            </div>
          </div>

          {error && (
            <div className="payment-error">
              <p>{error}</p>
            </div>
          )}

          <div className="payment-info">
            <h4>Payment Information</h4>
            <ul>
              <li>✅ Secure payment powered by Razorpay</li>
              <li>✅ Support for all major cards and UPI</li>
              <li>✅ Credits added instantly after payment</li>
              <li>✅ 256-bit SSL encryption</li>
            </ul>
          </div>

          <div className="payment-actions">
            <button 
              className="cancel-btn" 
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </button>
            <button 
              className="pay-btn" 
              onClick={handlePayment}
              disabled={loading}
            >
              {loading ? 'Processing...' : `Pay ₹${creditPackage.price}`}
            </button>
          </div>
        </div>

        <div className="payment-footer">
          <p>
            By proceeding, you agree to our{' '}
            <a href="/terms" target="_blank" rel="noopener noreferrer">
              Terms of Service
            </a>{' '}
            and{' '}
            <a href="/privacy" target="_blank" rel="noopener noreferrer">
              Privacy Policy
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default PaymentModal;
