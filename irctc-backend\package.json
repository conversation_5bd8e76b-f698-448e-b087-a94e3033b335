{"name": "irctc-backend", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "debug": "nodemon --inspect server.js"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "google-auth-library": "^9.15.1", "jsonwebtoken": "^9.0.0", "mongodb": "^6.15.0", "mongoose": "^7.0.0", "passport": "^0.6.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "razorpay": "^2.9.6"}, "devDependencies": {"nodemon": "^2.0.22"}}