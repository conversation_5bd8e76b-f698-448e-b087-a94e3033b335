// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is logged in
    chrome.storage.local.get(['authToken', 'userInfo'], function(result) {
        if (!result.authToken) {
            // Redirect to login page if not logged in
            window.location.href = 'popup.html';
            return;
        }

        // Initialize the page with user info
        initializePage(result.authToken, result.userInfo);
    });

    // Set up event listeners
    setupEventListeners();
});

// Initialize the page with user info
async function initializePage(authToken, userInfo) {
    try {
        // Update user info in the UI
        document.getElementById('user-name').textContent = userInfo.name;
        document.getElementById('user-avatar').src = userInfo.picture || 'images/default-avatar.png';

        // Fetch available booking credits
        await fetchCreditsCount(authToken);

        // Calculate custom plan price
        updateCustomPlanPrice();

    } catch (error) {
        console.error('Page initialization error:', error);
        showError('Failed to initialize page. Please try again.');
    }
}

// Fetch credits count from the server
async function fetchCreditsCount(authToken) {
    try {
        const response = await fetch('http://localhost:8000/api/users/credits', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        const data = await response.json();

        if (!response.ok) {
            // Check if token expired
            if (response.status === 401) {
                console.log('Token expired, redirecting to login');
                // Clear storage and redirect to login
                await chrome.storage.local.remove(['authToken', 'userInfo', 'isLoggedIn']);
                window.location.href = 'popup.html';
                return 0;
            }
            throw new Error('Failed to fetch credits');
        }

        // Update credits count in the UI
        document.getElementById('credits-count').textContent = data.credits;
        document.getElementById('sidebar-credits-count').textContent = data.credits;

        return data.credits;
    } catch (error) {
        console.error('Error fetching credits:', error);
        return 0;
    }
}

// Set up event listeners
function setupEventListeners() {
    // Plan selection buttons
    document.querySelectorAll('.btn-select-plan').forEach(button => {
        if (button.id !== 'btn-custom-plan') {
            button.addEventListener('click', function() {
                const plan = this.dataset.plan;
                const price = this.dataset.price;
                const credits = this.dataset.credits;

                showCheckout(plan, price, credits);
            });
        }
    });

    // Custom plan button
    document.getElementById('btn-custom-plan').addEventListener('click', function() {
        const credits = document.getElementById('custom-credits').value;
        const price = document.getElementById('custom-price').textContent;

        showCheckout('Custom', price, credits);
    });

    // Custom credits input
    document.getElementById('custom-credits').addEventListener('input', function() {
        updateCustomPlanPrice();
    });

    // Back button in checkout
    document.getElementById('btn-back').addEventListener('click', function() {
        hideCheckout();
    });

    // Proceed to payment button
    document.getElementById('btn-proceed').addEventListener('click', function() {
        proceedToPayment();
    });

    // Logout button
    document.getElementById('logout-btn').addEventListener('click', function() {
        logout();
    });

    // Help link
    document.getElementById('help-link').addEventListener('click', function(e) {
        e.preventDefault();
        alert('For help, <NAME_EMAIL>');
    });
}

// Update custom plan price based on credits
function updateCustomPlanPrice() {
    const credits = parseInt(document.getElementById('custom-credits').value) || 0;
    let price = 0;

    // Calculate price based on credits (with volume discount)
    if (credits <= 5) {
        price = credits * 40; // ₹40 per credit for small quantities
    } else if (credits <= 15) {
        price = credits * 35; // ₹35 per credit for medium quantities
    } else {
        price = credits * 30; // ₹30 per credit for large quantities
    }

    document.getElementById('custom-price').textContent = price;
}

// Show checkout section
function showCheckout(plan, price, credits) {
    // Update checkout summary
    document.getElementById('checkout-plan').textContent = plan;
    document.getElementById('checkout-credits').textContent = credits;
    document.getElementById('checkout-price').textContent = `₹${price}`;

    // Hide plans and show checkout
    document.querySelectorAll('.plans-container, .custom-plan').forEach(el => {
        el.classList.add('hidden');
    });

    document.getElementById('checkout-section').classList.remove('hidden');
}

// Hide checkout section
function hideCheckout() {
    // Show plans and hide checkout
    document.querySelectorAll('.plans-container, .custom-plan').forEach(el => {
        el.classList.remove('hidden');
    });

    document.getElementById('checkout-section').classList.add('hidden');
}

// Proceed to payment
async function proceedToPayment() {
    // Get checkout details
    const plan = document.getElementById('checkout-plan').textContent;
    const credits = document.getElementById('checkout-credits').textContent;
    const price = document.getElementById('checkout-price').textContent.replace('₹', '');
    const paymentMethod = document.querySelector('input[name="payment-method"]:checked').value;

    try {
        // Get auth token
        const result = await new Promise(resolve => {
            chrome.storage.local.get(['authToken', 'userInfo'], resolve);
        });

        if (!result.authToken) {
            showError('You are not logged in. Please log in and try again.');
            return;
        }

        // Show loading state
        const proceedBtn = document.getElementById('btn-proceed');
        const originalText = proceedBtn.innerHTML;
        proceedBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        proceedBtn.disabled = true;

        // Create Razorpay order
        const orderResponse = await fetch('http://localhost:8000/api/payment/create-order', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${result.authToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                amount: parseInt(price) * 100, // Convert to paise
                currency: 'INR'
            })
        });

        const orderData = await orderResponse.json();

        if (!orderResponse.ok) {
            throw new Error(orderData.message || 'Failed to create payment order');
        }

        // Initialize Razorpay payment
        const options = {
            key: orderData.key,
            amount: orderData.amount,
            currency: orderData.currency,
            name: 'IRCTC Auto Booking',
            description: `${credits} Credits Purchase`,
            order_id: orderData.orderId,
            handler: async function (response) {
                try {
                    // Verify payment
                    const verifyResponse = await fetch('http://localhost:8000/api/payment/verify', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${result.authToken}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            razorpay_order_id: response.razorpay_order_id,
                            razorpay_payment_id: response.razorpay_payment_id,
                            razorpay_signature: response.razorpay_signature,
                            credits: parseInt(credits),
                            amount: parseInt(price)
                        })
                    });

                    const verifyData = await verifyResponse.json();

                    if (!verifyResponse.ok) {
                        throw new Error(verifyData.message || 'Payment verification failed');
                    }

                    // Payment successful
                    alert(`Payment successful! ${credits} credits have been added to your account.`);

                    // Refresh credits count
                    await fetchCreditsCount(result.authToken);

                    // Redirect back to dashboard
                    window.location.href = 'dashboard.html';

                } catch (error) {
                    console.error('Payment verification error:', error);
                    showError('Payment verification failed: ' + error.message);
                }
            },
            prefill: {
                name: result.userInfo.name,
                email: result.userInfo.email
            },
            theme: {
                color: '#3399cc'
            },
            modal: {
                ondismiss: function() {
                    // Reset button state
                    proceedBtn.innerHTML = originalText;
                    proceedBtn.disabled = false;
                }
            }
        };

        // Load Razorpay script and open payment modal
        if (typeof Razorpay === 'undefined') {
            // Load Razorpay script dynamically
            const script = document.createElement('script');
            script.src = 'https://checkout.razorpay.com/v1/checkout.js';
            script.onload = function() {
                const rzp = new Razorpay(options);
                rzp.open();
            };
            script.onerror = function() {
                throw new Error('Failed to load Razorpay payment gateway');
            };
            document.head.appendChild(script);
        } else {
            const rzp = new Razorpay(options);
            rzp.open();
        }

        // Reset button state
        proceedBtn.innerHTML = originalText;
        proceedBtn.disabled = false;

    } catch (error) {
        console.error('Payment error:', error);
        showError('Payment failed: ' + error.message);

        // Reset button state
        const proceedBtn = document.getElementById('btn-proceed');
        proceedBtn.innerHTML = '<i class="fas fa-shopping-cart"></i> Proceed to Payment';
        proceedBtn.disabled = false;
    }
}

// Logout function
function logout() {
    chrome.storage.local.remove(['authToken', 'userInfo', 'isLoggedIn'], function() {
        window.location.href = 'popup.html';
    });
}

// Show error message
function showError(message) {
    alert(message);
}
