// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is logged in
    chrome.storage.local.get(['authToken', 'userInfo'], function(result) {
        if (!result.authToken) {
            // Redirect to login page if not logged in
            window.location.href = 'popup.html';
            return;
        }

        // Initialize the page with user info
        initializePage(result.authToken, result.userInfo);
    });

    // Set up event listeners
    setupEventListeners();
});

// Initialize the page with user info
async function initializePage(authToken, userInfo) {
    try {
        // Update user info in the UI
        document.getElementById('user-name').textContent = userInfo.name;
        document.getElementById('user-avatar').src = userInfo.picture || 'images/default-avatar.png';

        // Fetch available booking credits
        await fetchCreditsCount(authToken);

        // Calculate custom plan price
        updateCustomPlanPrice();

    } catch (error) {
        console.error('Page initialization error:', error);
        showError('Failed to initialize page. Please try again.');
    }
}

// Fetch credits count from the server
async function fetchCreditsCount(authToken) {
    try {
        const response = await fetch('http://localhost:3000/api/users/credits', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        const data = await response.json();

        if (!response.ok) {
            // Check if token expired
            if (response.status === 401) {
                console.log('Token expired, redirecting to login');
                // Clear storage and redirect to login
                await chrome.storage.local.remove(['authToken', 'userInfo', 'isLoggedIn']);
                window.location.href = 'popup.html';
                return 0;
            }
            throw new Error('Failed to fetch credits');
        }

        // Update credits count in the UI
        document.getElementById('credits-count').textContent = data.credits;
        document.getElementById('sidebar-credits-count').textContent = data.credits;

        return data.credits;
    } catch (error) {
        console.error('Error fetching credits:', error);
        return 0;
    }
}

// Set up event listeners
function setupEventListeners() {
    // Plan selection buttons
    document.querySelectorAll('.btn-select-plan').forEach(button => {
        if (button.id !== 'btn-custom-plan') {
            button.addEventListener('click', function() {
                const plan = this.dataset.plan;
                const price = this.dataset.price;
                const credits = this.dataset.credits;

                showCheckout(plan, price, credits);
            });
        }
    });

    // Custom plan button
    document.getElementById('btn-custom-plan').addEventListener('click', function() {
        const credits = document.getElementById('custom-credits').value;
        const price = document.getElementById('custom-price').textContent;

        showCheckout('Custom', price, credits);
    });

    // Custom credits input
    document.getElementById('custom-credits').addEventListener('input', function() {
        updateCustomPlanPrice();
    });

    // Back button in checkout
    document.getElementById('btn-back').addEventListener('click', function() {
        hideCheckout();
    });

    // Proceed to payment button
    document.getElementById('btn-proceed').addEventListener('click', function() {
        proceedToPayment();
    });

    // Logout button
    document.getElementById('logout-btn').addEventListener('click', function() {
        logout();
    });

    // Help link
    document.getElementById('help-link').addEventListener('click', function(e) {
        e.preventDefault();
        alert('For help, <NAME_EMAIL>');
    });
}

// Update custom plan price based on credits
function updateCustomPlanPrice() {
    const credits = parseInt(document.getElementById('custom-credits').value) || 0;
    let price = 0;

    // Calculate price based on credits (with volume discount)
    if (credits <= 5) {
        price = credits * 40; // ₹40 per credit for small quantities
    } else if (credits <= 15) {
        price = credits * 35; // ₹35 per credit for medium quantities
    } else {
        price = credits * 30; // ₹30 per credit for large quantities
    }

    document.getElementById('custom-price').textContent = price;
}

// Show checkout section
function showCheckout(plan, price, credits) {
    // Update checkout summary
    document.getElementById('checkout-plan').textContent = plan;
    document.getElementById('checkout-credits').textContent = credits;
    document.getElementById('checkout-price').textContent = `₹${price}`;

    // Hide plans and show checkout
    document.querySelectorAll('.plans-container, .custom-plan').forEach(el => {
        el.classList.add('hidden');
    });

    document.getElementById('checkout-section').classList.remove('hidden');
}

// Hide checkout section
function hideCheckout() {
    // Show plans and hide checkout
    document.querySelectorAll('.plans-container, .custom-plan').forEach(el => {
        el.classList.remove('hidden');
    });

    document.getElementById('checkout-section').classList.add('hidden');
}

// Proceed to payment
function proceedToPayment() {
    // Get checkout details
    const plan = document.getElementById('checkout-plan').textContent;
    const credits = document.getElementById('checkout-credits').textContent;
    const price = document.getElementById('checkout-price').textContent.replace('₹', '');
    const paymentMethod = document.querySelector('input[name="payment-method"]:checked').value;

    // Get auth token
    chrome.storage.local.get(['authToken', 'userInfo'], function(result) {
        if (!result.authToken) {
            showError('You are not logged in. Please log in and try again.');
            return;
        }

        // Create payment data
        const paymentData = {
            plan: plan,
            credits: parseInt(credits),
            price: parseInt(price),
            paymentMethod: paymentMethod,
            userId: result.userInfo.id
        };

        // Store payment data in local storage
        chrome.storage.local.set({ 'pendingPayment': paymentData }, function() {
            // Redirect to payment page on your website
            const paymentUrl = `https://irctcauto.com/payment?plan=${plan}&credits=${credits}&price=${price}&method=${paymentMethod}&token=${encodeURIComponent(result.authToken)}`;

            // Open payment page in new tab
            chrome.tabs.create({ url: paymentUrl });
        });
    });
}

// Logout function
function logout() {
    chrome.storage.local.remove(['authToken', 'userInfo', 'isLoggedIn'], function() {
        window.location.href = 'popup.html';
    });
}

// Show error message
function showError(message) {
    alert(message);
}
