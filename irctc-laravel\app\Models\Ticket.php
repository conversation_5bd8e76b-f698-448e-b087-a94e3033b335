<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Ticket extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'from_station',
        'to_station',
        'journey_date',
        'class',
        'status',
        'pnr',
        'train_number',
        'train_name',
        'boarding_station',
        'passengers',
        'infants',
        'mobile_number',
        'payment_method',
        'transaction_id',
        'booking_date',
    ];

    protected function casts(): array
    {
        return [
            'journey_date' => 'date',
            'booking_date' => 'datetime',
            'passengers' => 'array',
            'infants' => 'array',
        ];
    }

    /**
     * Get the user that owns the ticket.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if ticket is active
     */
    public function isActive()
    {
        return in_array($this->status, ['booked', 'confirmed']);
    }

    /**
     * Check if ticket can be cancelled
     */
    public function canBeCancelled()
    {
        return $this->status === 'booked' && $this->journey_date > now();
    }
}
