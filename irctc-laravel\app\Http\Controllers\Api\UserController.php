<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class UserController extends Controller
{
    /**
     * Get user's available credits
     */
    public function getCredits(Request $request)
    {
        try {
            $user = $request->user();

            return response()->json([
                'success' => true,
                'credits' => $user->available_tickets,
                'message' => 'Credits retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch credits',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update user preferences
     */
    public function updatePreferences(Request $request)
    {
        try {
            $user = $request->user();

            $request->validate([
                'payment_methods' => 'sometimes|array',
                'auto_fill_enabled' => 'sometimes|boolean',
            ]);

            // Update user preferences (you can add a preferences JSON column to users table)
            $user->update($request->only(['payment_methods', 'auto_fill_enabled']));

            return response()->json([
                'success' => true,
                'message' => 'Preferences updated successfully',
                'user' => $user
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update preferences',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Regenerate API key
     */
    public function regenerateApiKey(Request $request)
    {
        try {
            $user = $request->user();

            $user->update([
                'api_key' => Str::random(32)
            ]);

            return response()->json([
                'success' => true,
                'message' => 'API key regenerated successfully',
                'api_key' => $user->api_key
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to regenerate API key',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get user profile
     */
    public function getProfile(Request $request)
    {
        try {
            $user = $request->user();

            return response()->json([
                'success' => true,
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'picture' => $user->picture,
                    'available_tickets' => $user->available_tickets,
                    'status' => $user->status,
                    'api_key' => $user->api_key,
                    'last_login' => $user->last_login,
                    'created_at' => $user->created_at,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch profile',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
