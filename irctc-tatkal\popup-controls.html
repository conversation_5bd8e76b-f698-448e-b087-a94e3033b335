<!DOCTYPE html>
<html>
   <head>
      <title>IRCTC Automation Controls</title>
      <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
      <link rel="stylesheet" href="styles/popup.css">
      <style>
         /* Automation Control Styles */
         .automation-control-section {
            background: white;
            border-radius: 10px;
            padding: 12px;
            margin-bottom: 16px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
         }

         .automation-control-section h3 {
            margin-top: 0;
            margin-bottom: 8px;
            font-size: 18px;
            color: #333;
            text-align: center;
         }

         .control-info {
            color: #666;
            font-size: 14px;
            margin-bottom: 16px;
            text-align: center;
         }

         .control-buttons {
            display: flex;
            justify-content: space-between;
            gap: 10px;
            margin-bottom: 12px;
         }

         .btn {
            padding: 10px 20px;
            border-radius: 5px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            flex: 1;
         }

         .btn-warning {
            background: #ffc107;
            color: #212529;
         }

         .btn-warning:hover {
            background: #e0a800;
         }

         .btn-success {
            background: #34a853;
            color: white;
         }

         .btn-success:hover {
            background: #2d9348;
         }

         .btn-danger {
            background: #dc3545;
            color: white;
         }

         .btn-danger:hover {
            background: #c82333;
         }

         .automation-status {
            text-align: center;
            font-size: 14px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
            margin-top: 8px;
         }

         #status-text {
            font-weight: bold;
         }

         .status-running {
            color: #34a853;
         }

         .status-paused {
            color: #ffc107;
         }

         .status-stopped {
            color: #dc3545;
         }

         .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
         }

         .dashboard-link {
            text-align: center;
            margin-top: 16px;
         }

         .dashboard-link a {
            color: #4285f4;
            text-decoration: none;
         }

         .dashboard-link a:hover {
            text-decoration: underline;
         }
      </style>
   </head>
   <body>
      <div class="container">
         <div class="logo">
            <img src="icon16.png" alt="IRCTC Automation Logo">
         </div>

         <div class="welcome-text">
            <h1>IRCTC Auto Controls</h1>
            <p>Control your automation processes</p>
         </div>

         <!-- Automation Control Section -->
         <div class="automation-control-section">
            <h3>Automation Controls</h3>
            <p class="control-info">Control the background processes of the extension</p>
            <div class="control-buttons">
               <button id="toggle-automation-btn" class="btn btn-warning">
                  <i class="fas fa-pause"></i> Pause
               </button>
            </div>
            <div id="automation-status" class="automation-status">
               Status: <span id="status-text">Running</span>
            </div>
         </div>

         <div class="dashboard-link">
            <a href="dashboard.html">Go to Dashboard</a>
         </div>
      </div>

      <script src="popup-controls.js"></script>
   </body>
</html>
