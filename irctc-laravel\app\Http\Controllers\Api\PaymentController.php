<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Http\Request;
use Razorpay\Api\Api;

class PaymentController extends Controller
{
    protected $razorpay;

    public function __construct()
    {
        $this->razorpay = new Api(
            config('services.razorpay.key_id'),
            config('services.razorpay.key_secret')
        );
    }

    /**
     * Test endpoint to check payment service
     */
    public function test()
    {
        return response()->json([
            'success' => true,
            'message' => 'Payment service is working',
            'razorpay_configured' => !!(config('services.razorpay.key_id') && config('services.razorpay.key_secret')),
            'key_id' => config('services.razorpay.key_id') ? 'configured' : 'missing'
        ]);
    }

    /**
     * Create Razorpay order
     */
    public function createOrder(Request $request)
    {
        try {
            $request->validate([
                'amount' => 'required|integer|min:100', // Amount in paise
                'currency' => 'sometimes|string|in:INR'
            ]);

            $user = $request->user();
            $currency = $request->currency ?? 'INR';

            // Create Razorpay order
            $orderData = [
                'receipt' => 'receipt_' . $user->id . '_' . time(),
                'amount' => $request->amount,
                'currency' => $currency,
                'notes' => [
                    'user_id' => $user->id,
                    'order_type' => 'credit_purchase'
                ]
            ];

            $order = $this->razorpay->order->create($orderData);

            // Save transaction to database
            $transaction = Transaction::create([
                'user_id' => $user->id,
                'order_id' => $order['id'],
                'amount' => $request->amount / 100, // Store in rupees
                'currency' => $currency,
                'status' => 'created',
                'type' => 'credit_purchase'
            ]);

            return response()->json([
                'success' => true,
                'order_id' => $order['id'],
                'amount' => $order['amount'],
                'currency' => $order['currency'],
                'key' => config('services.razorpay.key_id')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create order',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Verify payment
     */
    public function verifyPayment(Request $request)
    {
        try {
            $request->validate([
                'razorpay_order_id' => 'required|string',
                'razorpay_payment_id' => 'required|string',
                'razorpay_signature' => 'required|string',
                'credits' => 'required|integer|min:1',
                'amount' => 'required|numeric|min:1'
            ]);

            $user = $request->user();

            // Verify signature
            $attributes = [
                'razorpay_order_id' => $request->razorpay_order_id,
                'razorpay_payment_id' => $request->razorpay_payment_id,
                'razorpay_signature' => $request->razorpay_signature
            ];

            $this->razorpay->utility->verifyPaymentSignature($attributes);

            // Find transaction
            $transaction = Transaction::where('order_id', $request->razorpay_order_id)
                                   ->where('user_id', $user->id)
                                   ->first();

            if (!$transaction) {
                return response()->json([
                    'success' => false,
                    'message' => 'Transaction not found'
                ], 404);
            }

            // Update transaction
            $transaction->update([
                'payment_id' => $request->razorpay_payment_id,
                'signature' => $request->razorpay_signature,
                'status' => 'completed',
                'credits' => $request->credits,
                'description' => "Purchase of {$request->credits} credits"
            ]);

            // Add credits to user account
            $user->increment('available_tickets', $request->credits);

            return response()->json([
                'success' => true,
                'message' => 'Payment verified successfully',
                'credits' => $request->credits,
                'new_balance' => $user->fresh()->available_tickets
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Payment verification failed',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get user transactions
     */
    public function getTransactions(Request $request)
    {
        try {
            $user = $request->user();
            $transactions = $user->transactions()
                                ->orderBy('created_at', 'desc')
                                ->paginate(20);

            return response()->json([
                'success' => true,
                'transactions' => $transactions,
                'message' => 'Transactions retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch transactions',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get specific transaction
     */
    public function getTransaction(Request $request, $transactionId)
    {
        try {
            $user = $request->user();
            $transaction = $user->transactions()->findOrFail($transactionId);

            return response()->json([
                'success' => true,
                'transaction' => $transaction,
                'message' => 'Transaction retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Transaction not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }
}
