const jwt = require('jsonwebtoken');
const User = require('../models/User');
const Admin = require('../models/Admin');

const auth = async (req, res, next) => {
    try {
        const token = req.header('Authorization')?.replace('Bearer ', '');
        console.log('Auth middleware - token received:', token ? 'Yes' : 'No');

        if (!token) {
            console.log('Auth middleware - no token provided');
            return res.status(401).json({ error: 'Authentication required' });
        }

        let decoded;
        try {
            decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key-here');
            console.log('Auth middleware - token decoded:', decoded);
        } catch (error) {
            console.error('Auth middleware error:', error);
            return res.status(401).json({ error: 'Token expired or invalid', tokenError: error.message });
        }

        // Check if this is an admin token
        if (decoded.id) {
            console.log('Auth middleware - checking for admin with id:', decoded.id);
            try {
                const admin = await Admin.findOne({ _id: decoded.id });
                if (admin) {
                    console.log('Auth middleware - admin found:', admin.email);
                    req.admin = admin;
                    req.token = token;
                    req.isAdmin = true;
                    return next();
                } else {
                    console.log('Auth middleware - no admin found with id:', decoded.id);
                }
            } catch (adminError) {
                console.error('Auth middleware - error finding admin:', adminError);
            }
        } else {
            console.log('Auth middleware - token does not contain id field');
        }

        // If not admin, check if it's a regular user
        console.log('Auth middleware - checking for user with id:', decoded.userId || decoded.id);
        const user = await User.findOne({
            _id: decoded.userId || decoded.id,
            status: 'active'
        });

        if (!user) {
            console.log('Auth middleware - no active user found');
            return res.status(401).json({ error: 'Invalid authentication' });
        }

        console.log('Auth middleware - user found:', user.email);
        req.user = user;
        req.token = token;
        next();
    } catch (error) {
        console.error('Auth middleware error:', error);
        res.status(401).json({ error: 'Invalid authentication' });
    }
};

module.exports = auth;