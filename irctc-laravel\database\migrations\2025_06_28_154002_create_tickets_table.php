<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tickets', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('from_station');
            $table->string('to_station');
            $table->date('journey_date');
            $table->enum('class', ['1A', '2A', '3A', 'SL', 'CC', 'EC']);
            $table->enum('status', ['booked', 'confirmed', 'cancelled', 'completed'])->default('booked');
            $table->string('pnr')->nullable();
            $table->string('train_number')->nullable();
            $table->string('train_name')->nullable();
            $table->string('boarding_station')->nullable();
            $table->json('passengers')->nullable(); // Store passenger details as JSON
            $table->json('infants')->nullable(); // Store infant details as JSON
            $table->string('mobile_number')->nullable();
            $table->string('payment_method')->nullable();
            $table->string('transaction_id')->nullable();
            $table->timestamp('booking_date')->useCurrent();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tickets');
    }
};
