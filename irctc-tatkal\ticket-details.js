// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is logged in
    chrome.storage.local.get(['authToken', 'userInfo'], function(result) {
        if (!result.authToken) {
            // Redirect to login page if not logged in
            window.location.href = 'popup.html';
            return;
        }
        
        // Initialize the page with user info
        initializePage(result.authToken, result.userInfo);
    });
    
    // Set up event listeners
    setupEventListeners();
});

// Initialize the page with user info
async function initializePage(authToken, userInfo) {
    try {
        // Update user info in the UI
        document.getElementById('user-name').textContent = userInfo.name;
        document.getElementById('user-avatar').src = userInfo.picture || 'images/default-avatar.png';
        
        // Fetch available booking credits
        await fetchCreditsCount(authToken);
        
        // Get ticket ID from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const ticketId = urlParams.get('id');
        
        if (!ticketId) {
            showError('No ticket ID provided');
            return;
        }
        
        // Fetch ticket details
        await fetchTicketDetails(authToken, ticketId);
        
    } catch (error) {
        console.error('Page initialization error:', error);
        showError('Failed to initialize page. Please try again.');
    }
}

// Fetch credits count from the server
async function fetchCreditsCount(authToken) {
    try {
        const response = await fetch('http://localhost:3000/api/tickets/count', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            // Check if token expired
            if (response.status === 401) {
                console.log('Token expired, redirecting to login');
                // Clear storage and redirect to login
                await chrome.storage.local.remove(['authToken', 'userInfo', 'isLoggedIn']);
                window.location.href = 'popup.html';
                return 0;
            }
            throw new Error('Failed to fetch credits');
        }
        
        // Update credits count in the UI
        document.getElementById('credits-count').textContent = data.count || 0;
        
        return data.count || 0;
    } catch (error) {
        console.error('Error fetching credits:', error);
        return 0;
    }
}

// Fetch ticket details from the server
async function fetchTicketDetails(authToken, ticketId) {
    try {
        // Show loading state
        document.getElementById('loading-container').style.display = 'flex';
        document.getElementById('ticket-content').style.display = 'none';
        
        const response = await fetch(`http://localhost:3000/api/tickets/${ticketId}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            // Check if token expired
            if (response.status === 401) {
                console.log('Token expired, redirecting to login');
                // Clear storage and redirect to login
                await chrome.storage.local.remove(['authToken', 'userInfo', 'isLoggedIn']);
                window.location.href = 'popup.html';
                return;
            }
            throw new Error(data.message || 'Failed to fetch ticket details');
        }
        
        // Update UI with ticket details
        updateTicketDetails(data.ticket);
        
        // Hide loading state and show content
        document.getElementById('loading-container').style.display = 'none';
        document.getElementById('ticket-content').style.display = 'block';
        
    } catch (error) {
        console.error('Error fetching ticket details:', error);
        showError('Failed to fetch ticket details. Please try again.');
        
        // Hide loading state
        document.getElementById('loading-container').style.display = 'none';
    }
}

// Update UI with ticket details
function updateTicketDetails(ticket) {
    // Update ticket status
    const statusElement = document.getElementById('ticket-status');
    statusElement.textContent = ticket.status;
    statusElement.className = `ticket-status ${ticket.status.toLowerCase()}`;
    
    // Update journey details
    document.getElementById('from-station').textContent = ticket.fromStation.name;
    document.getElementById('from-code').textContent = ticket.fromStation.code;
    document.getElementById('to-station').textContent = ticket.toStation.name;
    document.getElementById('to-code').textContent = ticket.toStation.code;
    document.getElementById('train-number').textContent = ticket.trainNumber;
    document.getElementById('train-name').textContent = ticket.trainName;
    document.getElementById('journey-date').textContent = formatDate(ticket.journeyDate);
    document.getElementById('journey-class').textContent = ticket.class;
    document.getElementById('journey-quota').textContent = getQuotaName(ticket.quota);
    document.getElementById('boarding-station').textContent = ticket.boardingStation ? 
        `${ticket.boardingStation.name} (${ticket.boardingStation.code})` : 
        `${ticket.fromStation.name} (${ticket.fromStation.code})`;
    
    // Update passenger details
    const passengersContainer = document.getElementById('passengers-container');
    passengersContainer.innerHTML = '';
    
    if (ticket.passengers && ticket.passengers.length > 0) {
        ticket.passengers.forEach((passenger, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${index + 1}</td>
                <td>${passenger.name}</td>
                <td>${passenger.age}</td>
                <td>${getGenderName(passenger.gender)}</td>
                <td>${passenger.berth || 'Not assigned'}</td>
                <td><span class="passenger-status ${getStatusClass(passenger.status)}">${passenger.status}</span></td>
            `;
            passengersContainer.appendChild(row);
        });
    } else {
        passengersContainer.innerHTML = '<tr><td colspan="6" class="no-data">No passenger details available</td></tr>';
    }
    
    // Update infant details
    const infantsContainer = document.getElementById('infants-container');
    infantsContainer.innerHTML = '';
    
    if (ticket.infants && ticket.infants.length > 0) {
        ticket.infants.forEach((infant, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${index + 1}</td>
                <td>${infant.name}</td>
                <td>${infant.age}</td>
                <td>${getGenderName(infant.gender)}</td>
            `;
            infantsContainer.appendChild(row);
        });
        document.getElementById('infant-section').style.display = 'block';
    } else {
        document.getElementById('infant-section').style.display = 'none';
    }
    
    // Update booking details
    document.getElementById('pnr-number').textContent = ticket.pnr || 'Not assigned yet';
    document.getElementById('booking-date').textContent = formatDateTime(ticket.bookingDate);
    document.getElementById('booking-status').textContent = ticket.status;
    document.getElementById('mobile-number').textContent = ticket.mobileNumber || 'Not available';
    document.getElementById('payment-method').textContent = ticket.paymentMethod || 'Not available';
    document.getElementById('transaction-id').textContent = ticket.transactionId || 'Not available';
    
    // Update cancel button visibility based on ticket status
    const cancelButton = document.getElementById('btn-cancel');
    if (ticket.status.toLowerCase() === 'cancelled' || ticket.status.toLowerCase() === 'completed') {
        cancelButton.style.display = 'none';
    } else {
        cancelButton.style.display = 'inline-flex';
    }
}

// Set up event listeners
function setupEventListeners() {
    // Back button
    document.getElementById('btn-back').addEventListener('click', function() {
        window.location.href = 'dashboard.html';
    });
    
    // Print button
    document.getElementById('btn-print').addEventListener('click', function() {
        window.print();
    });
    
    // Cancel button
    document.getElementById('btn-cancel').addEventListener('click', function() {
        cancelTicket();
    });
    
    // Logout button
    document.getElementById('logout-btn').addEventListener('click', function() {
        logout();
    });
    
    // Help link
    document.getElementById('help-link').addEventListener('click', function(e) {
        e.preventDefault();
        alert('For help, <NAME_EMAIL>');
    });
}

// Cancel ticket
async function cancelTicket() {
    if (!confirm('Are you sure you want to cancel this ticket? This action cannot be undone.')) {
        return;
    }
    
    try {
        // Get ticket ID from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const ticketId = urlParams.get('id');
        
        // Get auth token
        const result = await new Promise(resolve => {
            chrome.storage.local.get(['authToken'], resolve);
        });
        
        if (!result.authToken) {
            showError('You are not logged in. Please log in and try again.');
            return;
        }
        
        // Send cancel request to server
        const response = await fetch(`http://localhost:3000/api/tickets/${ticketId}/cancel`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${result.authToken}`,
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || 'Failed to cancel ticket');
        }
        
        // Show success message
        alert('Ticket cancelled successfully');
        
        // Refresh the page to show updated status
        window.location.reload();
        
    } catch (error) {
        console.error('Error cancelling ticket:', error);
        showError('Failed to cancel ticket. Please try again.');
    }
}

// Logout function
function logout() {
    chrome.storage.local.remove(['authToken', 'userInfo', 'isLoggedIn'], function() {
        window.location.href = 'popup.html';
    });
}

// Show error message
function showError(message) {
    alert(message);
}

// Helper function to format date
function formatDate(dateString) {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
}

// Helper function to format date and time
function formatDateTime(dateString) {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' };
    return date.toLocaleDateString('en-US', options);
}

// Helper function to get quota name
function getQuotaName(quota) {
    const quotaMap = {
        'GN': 'General',
        'TQ': 'Tatkal',
        'PT': 'Premium Tatkal',
        'LD': 'Ladies',
        'SR': 'Senior Citizen'
    };
    
    return quotaMap[quota] || quota;
}

// Helper function to get gender name
function getGenderName(gender) {
    const genderMap = {
        'M': 'Male',
        'F': 'Female',
        'O': 'Other'
    };
    
    return genderMap[gender] || gender;
}

// Helper function to get status class
function getStatusClass(status) {
    status = status.toLowerCase();
    
    if (status.includes('confirm') || status.includes('cnf')) {
        return 'confirmed';
    } else if (status.includes('rac')) {
        return 'rac';
    } else if (status.includes('wl') || status.includes('wait')) {
        return 'waitlisted';
    }
    
    return '';
}
