<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IRCTC Auto Dashboard</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="styles/dashboard.css">
</head>
<body>
    <div class="container">
        <!-- User Profile Section -->
        <div class="user-profile-section">
            <img id="user-avatar" class="user-avatar" src="" alt="User Avatar">
            <div class="user-details">
                <h2 id="user-name">Loading...</h2>
                <p id="user-email">Loading...</p>
            </div>
        </div>

        <!-- Tickets Section -->
        <div class="tickets-section">
            <div class="tickets-header">
                <div class="tickets-count-display">
                    <h3>Available Booking Credits</h3>
                    <div class="count-badge" id="tickets-count">0</div>
                </div>
                <button id="add-tickets-btn" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Buy More Credits
                </button>
            </div>
            <a href="#" id="detailed-booking-btn" class="btn btn-primary">
                <i class="fas fa-list-alt"></i> Detailed Booking
            </a>     
        </div>
        <!-- Payment Section -->
        <div class="payment-section">
            <h3>Purchase Booking Credits</h3>
            <p>Each credit allows you to book one train ticket with our auto-booking system.</p>
            <div class="payment-options">
                <a href="#" class="payment-option" data-quantity="1">
                    <span class="ticket-count">1 Credit</span>
                    <span class="price">₹99</span>
                </a>
                <a href="#" class="payment-option" data-quantity="5">
                    <span class="ticket-count">5 Credits</span>
                    <span class="price">₹449</span>
                    <span class="discount-badge">Save 10%</span>
                </a>
                <a href="#" class="payment-option" data-quantity="10">
                    <span class="ticket-count">10 Credits</span>
                    <span class="price">₹799</span>
                    <span class="discount-badge">Save 20%</span>
                </a>
            </div>
            <div class="payment-info">
                <p><i class="fas fa-info-circle"></i> Credits are used for our auto-booking system and are different from actual train tickets.</p>
            </div>
        </div>

        <!-- Saved Tickets Section -->
        <div class="saved-tickets-section">
            <div class="section-header">
                <h3 class="section-title">
                    <i class="fas fa-bookmark"></i>
                    Saved Tickets
                </h3>
                <div class="section-actions">
                    <button class="btn btn-outline-secondary btn-sm" onclick="clearAllTickets()">
                        <i class="fas fa-trash-alt"></i>
                        Clear All
                    </button>
                    <button class="btn btn-outline-primary btn-sm" onclick="refreshTickets()">
                        <i class="fas fa-sync-alt"></i>
                        Refresh
                    </button>
                </div>
            </div>

            <div id="saved-tickets-container" class="saved-tickets-container">
                <!-- Saved tickets will be displayed here -->
                <div class="empty-state" id="empty-tickets-state">
                    <div class="empty-icon">
                        <i class="fas fa-ticket-alt"></i>
                    </div>
                    <h4>No Saved Tickets</h4>
                    <p>Your saved ticket bookings will appear here. Use the "Detailed Booking" button to create new bookings.</p>
                    <button class="btn btn-primary" onclick="document.getElementById('detailed-booking-btn').click()">
                        <i class="fas fa-plus"></i>
                        Create First Booking
                    </button>
                </div>
            </div>
        </div>

        <!-- Logout Button -->
        <button class="logout-btn" id="logout-btn">
            <i class="fas fa-sign-out-alt"></i> Logout
        </button>
    </div>
    <script src="config.js"></script>
    <script src="dashboard.js"></script>
</body>
</html>