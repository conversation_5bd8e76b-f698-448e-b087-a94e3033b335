<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IRCTC Auto Dashboard</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="styles/dashboard.css">
</head>
<body>
    <div class="container">
        <!-- User Profile Section -->
        <div class="user-profile-section">
            <img id="user-avatar" class="user-avatar" src="" alt="User Avatar">
            <div class="user-details">
                <h2 id="user-name">Loading...</h2>
                <p id="user-email">Loading...</p>
            </div>
        </div>

        <!-- Tickets Section -->
        <div class="tickets-section">
            <div class="tickets-header">
                <div class="tickets-count-display">
                    <h3>Available Booking Credits</h3>
                    <div class="count-badge" id="tickets-count">0</div>
                </div>
                <button id="add-tickets-btn" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Buy More Credits
                </button>
            </div>
            <a href="#" id="detailed-booking-btn" class="btn btn-primary">
                <i class="fas fa-list-alt"></i> Detailed Booking
            </a>
            <!-- Booking Form -->
            <!-- <div class="booking-form">
                <h3>Book Train Ticket</h3>
                <p class="booking-info">Each booking uses 1 credit from your account</p>
                <form id="booking-form">
                    <div class="form-group">
                        <label for="from-station">From Station</label>
                        <input type="text" id="from-station" placeholder="e.g. NDLS" required>
                    </div>
                    <div class="form-group">
                        <label for="to-station">To Station</label>
                        <input type="text" id="to-station" placeholder="e.g. MMCT" required>
                    </div>
                    <div class="form-group">
                        <label for="journey-date">Journey Date</label>
                        <input type="date" id="journey-date" required>
                    </div>
                    <div class="form-group">
                        <label for="class">Class</label>
                        <select id="class" required>
                            <option value="1A">First AC (1A)</option>
                            <option value="2A">Second AC (2A)</option>
                            <option value="3A">Third AC (3A)</option>
                            <option value="SL">Sleeper (SL)</option>
                            <option value="CC">Chair Car (CC)</option>
                            <option value="EC">Executive Class (EC)</option>
                        </select>
                    </div>
                    <div class="booking-buttons">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-ticket-alt"></i> Quick Book
                        </button>
                        <a href="#" id="detailed-booking-btn" class="btn btn-primary">
                            <i class="fas fa-list-alt"></i> Detailed Booking
                        </a>
                    </div>
                </form>
            </div> -->
        </div>

        <!-- Booked Tickets Section -->
        <div class="booked-tickets-section">
            <h3>Your Train Bookings</h3>
            <p class="booking-info">Train tickets you've booked using our system</p>
            <div id="booked-tickets-container" class="tickets-container">
                <div class="loading-message">Loading your train bookings...</div>
            </div>
            <div id="no-tickets-message" class="no-tickets-message" style="display: none;">
                <i class="fas fa-ticket-alt"></i>
                <p>You haven't booked any train tickets yet.</p>
                <p class="small-text">Use your booking credits above to book a train ticket.</p>
            </div>
        </div>

        <!-- Saved Tickets Section -->
        <div class="saved-tickets-section">
            <h3>Your Saved Tickets</h3>
            <p class="booking-info">Ticket details you've saved for quick booking</p>
            <div class="tickets-header">
                <div></div>
                <button id="refresh-saved-tickets-btn" class="btn btn-secondary">
                    <i class="fas fa-sync"></i> Refresh Saved Tickets
                </button>
            </div>
            <div id="saved-tickets-container" class="tickets-container">
                <div class="loading-message">Loading your saved tickets...</div>
            </div>
            <div id="no-saved-tickets-message" class="no-tickets-message" style="display: none;">
                <i class="fas fa-save"></i>
                <p>You haven't saved any ticket details yet.</p>
                <p class="small-text">Save ticket details during booking for quick access later.</p>
            </div>
        </div>

        <!-- Payment Section -->
        <div class="payment-section">
            <h3>Purchase Booking Credits</h3>
            <p>Each credit allows you to book one train ticket with our auto-booking system.</p>
            <div class="payment-options">
                <a href="add-credits.html" class="payment-option" data-quantity="1">
                    <span class="ticket-count">1 Credit</span>
                    <span class="price">₹99</span>
                </a>
                <a href="add-credits.html" class="payment-option" data-quantity="5">
                    <span class="ticket-count">5 Credits</span>
                    <span class="price">₹449</span>
                    <span class="discount-badge">Save 10%</span>
                </a>
                <a href="add-credits.html" class="payment-option" data-quantity="10">
                    <span class="ticket-count">10 Credits</span>
                    <span class="price">₹799</span>
                    <span class="discount-badge">Save 20%</span>
                </a>
            </div>
            <div class="payment-info">
                <p><i class="fas fa-info-circle"></i> Credits are used for our auto-booking system and are different from actual train tickets.</p>
            </div>
        </div>

        <!-- Logout Button -->
        <button class="logout-btn" id="logout-btn">
            <i class="fas fa-sign-out-alt"></i> Logout
        </button>
    </div>
    <script src="dashboard.js"></script>
</body>
</html>