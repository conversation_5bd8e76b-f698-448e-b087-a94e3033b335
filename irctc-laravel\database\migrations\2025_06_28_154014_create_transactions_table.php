<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('order_id')->unique();
            $table->string('payment_id')->nullable();
            $table->string('signature')->nullable();
            $table->decimal('amount', 10, 2); // Amount in rupees
            $table->string('currency', 3)->default('INR');
            $table->integer('credits')->default(0);
            $table->enum('status', ['created', 'pending', 'completed', 'failed', 'cancelled'])->default('created');
            $table->enum('type', ['credit_purchase', 'refund', 'bonus'])->default('credit_purchase');
            $table->string('description')->nullable();
            $table->json('metadata')->nullable(); // Additional data
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
