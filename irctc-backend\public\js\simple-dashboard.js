// Simple dashboard.js
let token = localStorage.getItem('adminToken');

// Check authentication
if (!token) {
    window.location.href = '/login';
}

// DOM ready
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded - simple dashboard.js');
    fetchStats();
    fetchUsers();

    // Logout button
    document.getElementById('logout-btn').addEventListener('click', () => {
        localStorage.removeItem('adminToken');
        window.location.href = '/login';
    });
});

// Fetch stats
async function fetchStats() {
    try {
        console.log('Fetching stats...');

        // Check if token exists
        if (!token) {
            console.error('No token found');
            localStorage.removeItem('adminToken');
            window.location.href = '/login';
            return;
        }

        const response = await fetch('/api/admin/stats', {
            headers: {
                'Authorization': `Bear<PERSON> ${token}`
            }
        });

        // Check for authentication errors
        if (response.status === 401 || response.status === 403) {
            console.error('Authentication error:', response.status);
            localStorage.removeItem('adminToken');
            window.location.href = '/login';
            return;
        }

        if (!response.ok) {
            throw new Error(`Failed to fetch stats: ${response.status}`);
        }

        const data = await response.json();
        console.log('Stats data:', data);

        // Update UI
        document.getElementById('total-users').textContent = data.totalUsers;
        document.getElementById('active-users').textContent = data.activeUsers;
        document.getElementById('new-users').textContent = data.lastWeekUsers;

    } catch (error) {
        console.error('Error fetching stats:', error);
        document.getElementById('total-users').textContent = 'Error';
        document.getElementById('active-users').textContent = 'Error';
        document.getElementById('new-users').textContent = 'Error';
    }
}

// Fetch users
async function fetchUsers() {
    try {
        console.log('Fetching users...');
        const tbody = document.getElementById('users-table-body');

        // Show loading
        tbody.innerHTML = '<tr><td colspan="6" class="text-center py-4">Loading users...</td></tr>';

        // Check if token exists
        if (!token) {
            console.error('No token found');
            localStorage.removeItem('adminToken');
            window.location.href = '/login';
            return;
        }

        const response = await fetch('/api/admin/users', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        // Check for authentication errors
        if (response.status === 401 || response.status === 403) {
            console.error('Authentication error:', response.status);
            localStorage.removeItem('adminToken');
            window.location.href = '/login';
            return;
        }

        if (!response.ok) {
            throw new Error(`Failed to fetch users: ${response.status}`);
        }

        const data = await response.json();
        console.log('Users data:', data);

        // Clear table
        tbody.innerHTML = '';

        // Check if we have users
        if (!data.users || data.users.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center py-4">No users found</td></tr>';
            return;
        }

        // Add users to table
        data.users.forEach(user => {
            const row = document.createElement('tr');

            // User info cell
            const userInfoCell = document.createElement('td');
            userInfoCell.className = 'px-6 py-4 whitespace-nowrap';
            userInfoCell.innerHTML = `
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                        <img class="h-10 w-10 rounded-full" src="${user.picture || '/img/default-avatar.png'}" alt="">
                    </div>
                    <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">${user.name || 'N/A'}</div>
                        <div class="text-sm text-gray-500">Joined: ${user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}</div>
                    </div>
                </div>
            `;
            row.appendChild(userInfoCell);

            // Contact cell
            const contactCell = document.createElement('td');
            contactCell.className = 'px-6 py-4 whitespace-nowrap';
            contactCell.innerHTML = `
                <div class="text-sm text-gray-900">${user.email || 'N/A'}</div>
                <div class="text-sm text-gray-500">Google: ${user.googleId || 'Not Connected'}</div>
            `;
            row.appendChild(contactCell);

            // Status cell
            const statusCell = document.createElement('td');
            statusCell.className = 'px-6 py-4 whitespace-nowrap';
            statusCell.innerHTML = `
                <select class="status-select rounded-md border-gray-300 shadow-sm" data-user-id="${user._id}">
                    <option value="active" ${user.status === 'active' ? 'selected' : ''}>Active</option>
                    <option value="suspended" ${user.status === 'suspended' ? 'selected' : ''}>Suspended</option>
                    <option value="deleted" ${user.status === 'deleted' ? 'selected' : ''}>Deleted</option>
                </select>
            `;
            row.appendChild(statusCell);

            // Account details cell
            const detailsCell = document.createElement('td');
            detailsCell.className = 'px-6 py-4 whitespace-nowrap';
            detailsCell.innerHTML = `
                <div class="text-sm text-gray-500">API Key: ${user.apiKey || 'None'}</div>
                <div class="text-sm text-gray-500">Last Login: ${user.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'Never'}</div>
            `;
            row.appendChild(detailsCell);

            // Tickets cell
            const ticketsCell = document.createElement('td');
            ticketsCell.className = 'px-6 py-4 whitespace-nowrap';
            ticketsCell.innerHTML = `
                <div class="text-center">
                    <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                        ${user.availableTickets || 0}
                    </span>
                </div>
            `;
            row.appendChild(ticketsCell);

            // Actions cell
            const actionsCell = document.createElement('td');
            actionsCell.className = 'px-6 py-4 whitespace-nowrap text-center text-sm font-medium';
            actionsCell.innerHTML = `
                <a href="/user-details?id=${user._id}" class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    View Details
                </a>
            `;
            row.appendChild(actionsCell);

            // Add row to table
            tbody.appendChild(row);
        });

        console.log('Users table populated successfully');

    } catch (error) {
        console.error('Error fetching users:', error);
        const tbody = document.getElementById('users-table-body');
        tbody.innerHTML = `<tr><td colspan="6" class="text-center py-4 text-red-500">Error: ${error.message}</td></tr>`;
    }
}
