import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import Layout from './Layout';
import '../styles/StaticPages.css';

const Support = () => {
  const [activeTab, setActiveTab] = useState('faq');
  const [contactForm, setContactForm] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });
  const [formSubmitted, setFormSubmitted] = useState(false);

  const handleInputChange = (e) => {
    setContactForm({
      ...contactForm,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Here you would typically send the form data to your backend
    console.log('Support form submitted:', contactForm);
    setFormSubmitted(true);
    setContactForm({ name: '', email: '', subject: '', message: '' });
  };

  const faqData = [
    {
      question: "How does the IRCTC Tatkal Extension work?",
      answer: "Our Chrome extension automates the ticket booking process on the IRCTC website. You fill out the booking details in our extension, and it automatically fills and submits the forms during Tatkal booking hours."
    },
    {
      question: "What are credits and how do I use them?",
      answer: "Credits are the currency used in our system. Each booking attempt consumes one credit. You can purchase credits through our website using secure payment methods. Credits are consumed whether the booking is successful or not."
    },
    {
      question: "Is my payment information secure?",
      answer: "Yes, absolutely. We use Razorpay, a trusted payment gateway, to process all payments. We never store your payment information on our servers. All transactions are encrypted and secure."
    },
    {
      question: "What happens if my booking fails?",
      answer: "If a booking fails due to technical issues on our end, we will refund the credit to your account. However, if the booking fails due to IRCTC-related issues (like no availability), the credit is consumed as the attempt was made."
    },
    {
      question: "Can I get a refund for unused credits?",
      answer: "Credits are generally non-refundable as mentioned in our Terms of Service. However, in exceptional circumstances, please contact our support team, and we'll review your case individually."
    },
    {
      question: "How do I install and set up the extension?",
      answer: "Download the extension from the Chrome Web Store, install it, and then use your API key from the dashboard to authenticate. Follow the step-by-step guide in your user dashboard."
    },
    {
      question: "Why is my extension not working?",
      answer: "Common issues include: outdated extension version, incorrect API key, browser compatibility issues, or IRCTC website changes. Try updating the extension and checking your API key first."
    },
    {
      question: "Can I use the extension on multiple devices?",
      answer: "Yes, you can use your account and API key on multiple devices. However, simultaneous bookings from multiple devices may cause conflicts."
    }
  ];

  return (
    <Layout>
      <div className="static-page">
        <div className="static-container">
        <header className="static-header">
          <Link to="/" className="back-link">← Back to Home</Link>
          <h1>Support Center</h1>
          <p className="subtitle">We're here to help you with any questions or issues</p>
        </header>

        <div className="support-tabs">
          <button
            className={`tab-btn ${activeTab === 'faq' ? 'active' : ''}`}
            onClick={() => setActiveTab('faq')}
          >
            FAQ
          </button>
          <button
            className={`tab-btn ${activeTab === 'contact' ? 'active' : ''}`}
            onClick={() => setActiveTab('contact')}
          >
            Contact Us
          </button>
          <button
            className={`tab-btn ${activeTab === 'guides' ? 'active' : ''}`}
            onClick={() => setActiveTab('guides')}
          >
            Guides
          </button>
        </div>

        <div className="static-content">
          {activeTab === 'faq' && (
            <section className="faq-section">
              <h2>Frequently Asked Questions</h2>
              <div className="faq-list">
                {faqData.map((faq, index) => (
                  <div key={index} className="faq-item">
                    <h3>{faq.question}</h3>
                    <p>{faq.answer}</p>
                  </div>
                ))}
              </div>
            </section>
          )}

          {activeTab === 'contact' && (
            <section className="contact-section">
              <h2>Contact Our Support Team</h2>

              <div className="contact-methods">
                <div className="contact-method">
                  <h3>📧 Email Support</h3>
                  <p>Get help via email within 24 hours</p>
                  <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>

                <div className="contact-method">
                  <h3>💬 Live Chat</h3>
                  <p>Chat with our support team</p>
                  <p>Available: Mon-Fri, 9 AM - 6 PM IST</p>
                </div>

                <div className="contact-method">
                  <h3>📱 WhatsApp</h3>
                  <p>Quick support via WhatsApp</p>
                  <a href="https://wa.me/919876543210">+91 98765 43210</a>
                </div>
              </div>

              <div className="contact-form-container">
                <h3>Send us a Message</h3>
                {formSubmitted ? (
                  <div className="form-success">
                    <h4>✅ Message Sent Successfully!</h4>
                    <p>Thank you for contacting us. We'll get back to you within 24 hours.</p>
                  </div>
                ) : (
                  <form className="contact-form" onSubmit={handleSubmit}>
                    <div className="form-group">
                      <label htmlFor="name">Name *</label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={contactForm.name}
                        onChange={handleInputChange}
                        required
                      />
                    </div>

                    <div className="form-group">
                      <label htmlFor="email">Email *</label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={contactForm.email}
                        onChange={handleInputChange}
                        required
                      />
                    </div>

                    <div className="form-group">
                      <label htmlFor="subject">Subject *</label>
                      <select
                        id="subject"
                        name="subject"
                        value={contactForm.subject}
                        onChange={handleInputChange}
                        required
                      >
                        <option value="">Select a subject</option>
                        <option value="technical">Technical Issue</option>
                        <option value="billing">Billing & Payments</option>
                        <option value="refund">Refund Request</option>
                        <option value="feature">Feature Request</option>
                        <option value="other">Other</option>
                      </select>
                    </div>

                    <div className="form-group">
                      <label htmlFor="message">Message *</label>
                      <textarea
                        id="message"
                        name="message"
                        rows="5"
                        value={contactForm.message}
                        onChange={handleInputChange}
                        placeholder="Please describe your issue or question in detail..."
                        required
                      ></textarea>
                    </div>

                    <button type="submit" className="submit-btn">
                      Send Message
                    </button>
                  </form>
                )}
              </div>
            </section>
          )}

          {activeTab === 'guides' && (
            <section className="guides-section">
              <h2>User Guides</h2>

              <div className="guide-cards">
                <div className="guide-card">
                  <h3>🚀 Getting Started</h3>
                  <p>Learn how to set up your account and start using the extension</p>
                  <ul>
                    <li>Create an account using Google/Facebook</li>
                    <li>Purchase credits for booking</li>
                    <li>Install the Chrome extension</li>
                    <li>Configure your API key</li>
                  </ul>
                </div>

                <div className="guide-card">
                  <h3>💳 Managing Credits</h3>
                  <p>Everything you need to know about our credit system</p>
                  <ul>
                    <li>How to purchase credits</li>
                    <li>Understanding credit consumption</li>
                    <li>Viewing transaction history</li>
                    <li>Credit refund policies</li>
                  </ul>
                </div>

                <div className="guide-card">
                  <h3>🎫 Booking Tickets</h3>
                  <p>Step-by-step guide to automated ticket booking</p>
                  <ul>
                    <li>Setting up passenger details</li>
                    <li>Configuring booking preferences</li>
                    <li>Understanding booking status</li>
                    <li>Troubleshooting failed bookings</li>
                  </ul>
                </div>

                <div className="guide-card">
                  <h3>🔧 Troubleshooting</h3>
                  <p>Common issues and their solutions</p>
                  <ul>
                    <li>Extension not loading</li>
                    <li>Authentication errors</li>
                    <li>Payment failures</li>
                    <li>Browser compatibility issues</li>
                  </ul>
                </div>
              </div>
            </section>
          )}
        </div>
      </div>
    </div>
    </Layout>
  );
};

export default Support;
