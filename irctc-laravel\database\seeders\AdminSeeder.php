<?php

namespace Database\Seeders;

use App\Models\Admin;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create default admin user
        Admin::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'role' => 'super_admin',
                'status' => 'active',
            ]
        );

        // Create additional admin if needed
        Admin::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Support Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('support123'),
                'role' => 'admin',
                'status' => 'active',
            ]
        );
    }
}
