<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laravel API Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container py-5">
        <h1 class="mb-4">Laravel API Test Suite</h1>
        
        <!-- Server Status -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-server me-2"></i>Server Status</h5>
            </div>
            <div class="card-body">
                <button class="btn btn-primary" onclick="testServerHealth()">Test Server Health</button>
                <div id="server-result" class="mt-3"></div>
            </div>
        </div>

        <!-- API Endpoints -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-plug me-2"></i>API Endpoints</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <button class="btn btn-outline-primary w-100" onclick="testPaymentService()">
                            Test Payment Service
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-outline-info w-100" onclick="testCreditsAPI()">
                            Test Credits API (requires auth)
                        </button>
                    </div>
                </div>
                <div id="api-result" class="mt-3"></div>
            </div>
        </div>

        <!-- Authentication Test -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-key me-2"></i>Authentication Test</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="testToken" class="form-label">Test Token (optional)</label>
                    <input type="text" class="form-control" id="testToken" placeholder="Enter Bearer token for testing">
                </div>
                <button class="btn btn-warning" onclick="testAuthEndpoint()">Test Auth Endpoint</button>
                <div id="auth-result" class="mt-3"></div>
            </div>
        </div>

        <!-- Database Test -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-database me-2"></i>Database Test</h5>
            </div>
            <div class="card-body">
                <button class="btn btn-success" onclick="testDatabase()">Test Database Connection</button>
                <div id="db-result" class="mt-3"></div>
            </div>
        </div>

        <!-- Results -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-line me-2"></i>Test Results</h5>
            </div>
            <div class="card-body">
                <div id="overall-results">
                    <p class="text-muted">Run tests to see results here.</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const API_BASE = 'http://localhost:8000';
        let testResults = [];

        function showResult(containerId, status, message, data = null) {
            const container = document.getElementById(containerId);
            const alertClass = status === 'success' ? 'alert-success' : 
                              status === 'warning' ? 'alert-warning' : 'alert-danger';
            
            let html = `
                <div class="alert ${alertClass}">
                    <strong>${status.toUpperCase()}:</strong> ${message}
                </div>
            `;
            
            if (data) {
                html += `
                    <div class="card">
                        <div class="card-body">
                            <h6>Response Data:</h6>
                            <pre class="bg-light p-3 rounded"><code>${JSON.stringify(data, null, 2)}</code></pre>
                        </div>
                    </div>
                `;
            }
            
            container.innerHTML = html;
            
            // Add to overall results
            testResults.push({
                test: containerId.replace('-result', ''),
                status: status,
                message: message,
                timestamp: new Date().toLocaleTimeString()
            });
            
            updateOverallResults();
        }

        function updateOverallResults() {
            const container = document.getElementById('overall-results');
            let html = '<h6>Test Summary:</h6><ul class="list-group">';
            
            testResults.forEach(result => {
                const iconClass = result.status === 'success' ? 'fa-check text-success' : 
                                 result.status === 'warning' ? 'fa-exclamation text-warning' : 'fa-times text-danger';
                
                html += `
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="fas ${iconClass} me-2"></i>${result.test}: ${result.message}</span>
                        <small class="text-muted">${result.timestamp}</small>
                    </li>
                `;
            });
            
            html += '</ul>';
            container.innerHTML = html;
        }

        async function testServerHealth() {
            try {
                const response = await fetch(`${API_BASE}/api/health`);
                const data = await response.json();
                
                if (response.ok) {
                    showResult('server-result', 'success', 'Server is running properly', data);
                } else {
                    showResult('server-result', 'error', 'Server responded with error', data);
                }
            } catch (error) {
                showResult('server-result', 'error', `Server connection failed: ${error.message}`);
            }
        }

        async function testPaymentService() {
            try {
                const response = await fetch(`${API_BASE}/api/payment/test`);
                const data = await response.json();
                
                if (response.ok) {
                    showResult('api-result', 'success', 'Payment service is working', data);
                } else {
                    showResult('api-result', 'error', 'Payment service error', data);
                }
            } catch (error) {
                showResult('api-result', 'error', `Payment service connection failed: ${error.message}`);
            }
        }

        async function testCreditsAPI() {
            try {
                const token = document.getElementById('testToken').value;
                const headers = {
                    'Content-Type': 'application/json'
                };
                
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }
                
                const response = await fetch(`${API_BASE}/api/users/credits`, {
                    headers: headers
                });
                const data = await response.json();
                
                if (response.status === 401) {
                    showResult('api-result', 'warning', 'Authentication required (expected)', data);
                } else if (response.ok) {
                    showResult('api-result', 'success', 'Credits API working', data);
                } else {
                    showResult('api-result', 'error', 'Credits API error', data);
                }
            } catch (error) {
                showResult('api-result', 'error', `Credits API connection failed: ${error.message}`);
            }
        }

        async function testAuthEndpoint() {
            try {
                const token = document.getElementById('testToken').value;
                
                if (!token) {
                    showResult('auth-result', 'warning', 'No token provided - testing without authentication');
                    return;
                }
                
                const response = await fetch(`${API_BASE}/api/auth/me`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                const data = await response.json();
                
                if (response.ok) {
                    showResult('auth-result', 'success', 'Authentication successful', data);
                } else {
                    showResult('auth-result', 'error', 'Authentication failed', data);
                }
            } catch (error) {
                showResult('auth-result', 'error', `Auth endpoint connection failed: ${error.message}`);
            }
        }

        async function testDatabase() {
            try {
                // Test database by checking if we can fetch users count
                const response = await fetch(`${API_BASE}/`);
                
                if (response.ok) {
                    showResult('db-result', 'success', 'Database connection working (Laravel app loaded successfully)');
                } else {
                    showResult('db-result', 'error', 'Database connection may have issues');
                }
            } catch (error) {
                showResult('db-result', 'error', `Database test failed: ${error.message}`);
            }
        }

        // Auto-run server health check on page load
        document.addEventListener('DOMContentLoaded', function() {
            testServerHealth();
        });
    </script>
</body>
</html>
