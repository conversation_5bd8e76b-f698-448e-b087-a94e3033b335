<?php $__env->startSection('title', 'Admin Dashboard'); ?>
<?php $__env->startSection('page-title', 'Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3><?php echo e(number_format($stats['total_users'])); ?></h3>
                    <p>Total Users</p>
                </div>
                <div class="text-primary">
                    <i class="fas fa-users fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card success">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3><?php echo e(number_format($stats['active_users'])); ?></h3>
                    <p>Active Users</p>
                </div>
                <div class="text-success">
                    <i class="fas fa-user-check fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card warning">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3><?php echo e(number_format($stats['total_tickets'])); ?></h3>
                    <p>Total Tickets</p>
                </div>
                <div class="text-warning">
                    <i class="fas fa-train fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card danger">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3>₹<?php echo e(number_format($stats['total_revenue'], 2)); ?></h3>
                    <p>Total Revenue</p>
                </div>
                <div class="text-success">
                    <i class="fas fa-rupee-sign fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional Stats -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Quick Stats</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <h4 class="text-info"><?php echo e(number_format($stats['pending_transactions'])); ?></h4>
                        <small class="text-muted">Pending Transactions</small>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-success">99.2%</h4>
                        <small class="text-muted">Success Rate</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-clock me-2"></i>System Status</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>API Server</span>
                    <span class="badge bg-success">Online</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>Payment Gateway</span>
                    <span class="badge bg-success">Connected</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>Database</span>
                    <span class="badge bg-success">Healthy</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>Extension Service</span>
                    <span class="badge bg-success">Active</span>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Users -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-user-plus me-2"></i>Recent Users</h5>
                <a href="<?php echo e(route('admin.users')); ?>" class="btn btn-sm btn-outline-light">View All</a>
            </div>
            <div class="card-body">
                <?php if($recentUsers->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Credits</th>
                                    <th>Status</th>
                                    <th>Joined</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $recentUsers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php if($user->picture): ?>
                                                    <img src="<?php echo e($user->picture); ?>" alt="Profile" class="rounded-circle me-2" width="32" height="32">
                                                <?php else: ?>
                                                    <div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;">
                                                        <i class="fas fa-user fa-sm"></i>
                                                    </div>
                                                <?php endif; ?>
                                                <div>
                                                    <div class="fw-bold"><?php echo e($user->name); ?></div>
                                                    <small class="text-muted"><?php echo e($user->email); ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo e($user->available_tickets); ?></span>
                                        </td>
                                        <td>
                                            <?php switch($user->status):
                                                case ('active'): ?>
                                                    <span class="badge bg-success">Active</span>
                                                    <?php break; ?>
                                                <?php case ('suspended'): ?>
                                                    <span class="badge bg-warning">Suspended</span>
                                                    <?php break; ?>
                                                <?php default: ?>
                                                    <span class="badge bg-secondary"><?php echo e(ucfirst($user->status)); ?></span>
                                            <?php endswitch; ?>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?php echo e($user->created_at->diffForHumans()); ?></small>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No users found</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Recent Transactions -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>Recent Transactions</h5>
                <a href="<?php echo e(route('admin.transactions')); ?>" class="btn btn-sm btn-outline-light">View All</a>
            </div>
            <div class="card-body">
                <?php if($recentTransactions->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Amount</th>
                                    <th>Credits</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $recentTransactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <div>
                                                <div class="fw-bold"><?php echo e($transaction->user->name); ?></div>
                                                <small class="text-muted"><?php echo e($transaction->user->email); ?></small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="fw-bold">₹<?php echo e(number_format($transaction->amount, 2)); ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo e($transaction->credits); ?></span>
                                        </td>
                                        <td>
                                            <?php switch($transaction->status):
                                                case ('completed'): ?>
                                                    <span class="badge bg-success">Completed</span>
                                                    <?php break; ?>
                                                <?php case ('pending'): ?>
                                                    <span class="badge bg-warning">Pending</span>
                                                    <?php break; ?>
                                                <?php case ('failed'): ?>
                                                    <span class="badge bg-danger">Failed</span>
                                                    <?php break; ?>
                                                <?php default: ?>
                                                    <span class="badge bg-secondary"><?php echo e(ucfirst($transaction->status)); ?></span>
                                            <?php endswitch; ?>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?php echo e($transaction->created_at->diffForHumans()); ?></small>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No transactions found</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-3">
                        <a href="<?php echo e(route('admin.users')); ?>" class="btn btn-outline-primary w-100 py-3">
                            <i class="fas fa-users d-block mb-2"></i>
                            Manage Users
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="<?php echo e(route('admin.transactions')); ?>" class="btn btn-outline-success w-100 py-3">
                            <i class="fas fa-credit-card d-block mb-2"></i>
                            View Transactions
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="<?php echo e(route('admin.tickets')); ?>" class="btn btn-outline-warning w-100 py-3">
                            <i class="fas fa-train d-block mb-2"></i>
                            View Tickets
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="<?php echo e(route('home')); ?>" target="_blank" class="btn btn-outline-info w-100 py-3">
                            <i class="fas fa-external-link-alt d-block mb-2"></i>
                            View Website
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Auto-refresh stats every 30 seconds
setInterval(function() {
    // You can implement AJAX refresh here if needed
}, 30000);
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\chrome-ex\irctc-laravel\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>