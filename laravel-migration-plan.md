# Laravel Migration Plan for IRCTC Extension Project

## New Laravel Project Structure

```
irctc-laravel/
├── app/
│   ├── Http/
│   │   ├── Controllers/
│   │   │   ├── Api/
│   │   │   │   ├── AuthController.php          # Chrome extension auth
│   │   │   │   ├── UserController.php          # User management API
│   │   │   │   ├── TicketController.php        # Ticket booking API
│   │   │   │   └── PaymentController.php       # Payment processing API
│   │   │   ├── Web/
│   │   │   │   ├── HomeController.php          # Landing page
│   │   │   │   ├── DashboardController.php     # User dashboard
│   │   │   │   └── AdminController.php         # Admin panel
│   │   │   └── Auth/
│   │   │       ├── GoogleController.php        # Google OAuth
│   │   │       └── FacebookController.php      # Facebook OAuth
│   │   ├── Middleware/
│   │   │   ├── ApiAuth.php                     # API authentication
│   │   │   └── AdminAuth.php                   # Admin authentication
│   │   └── Requests/
│   │       ├── BookTicketRequest.php
│   │       └── PaymentRequest.php
│   ├── Models/
│   │   ├── User.php                            # Enhanced user model
│   │   ├── Ticket.php                          # Ticket model
│   │   ├── Transaction.php                     # Payment transactions
│   │   └── Admin.php                           # Admin model
│   ├── Services/
│   │   ├── PaymentService.php                  # Razorpay integration
│   │   ├── TicketService.php                   # Ticket booking logic
│   │   └── AuthService.php                     # OAuth services
│   └── Providers/
│       └── AppServiceProvider.php
├── database/
│   ├── migrations/
│   │   ├── create_users_table.php
│   │   ├── create_tickets_table.php
│   │   ├── create_transactions_table.php
│   │   └── create_admins_table.php
│   ├── seeders/
│   │   └── AdminSeeder.php
│   └── factories/
├── resources/
│   ├── views/
│   │   ├── layouts/
│   │   │   ├── app.blade.php                   # Main layout
│   │   │   └── admin.blade.php                 # Admin layout
│   │   ├── home/
│   │   │   ├── index.blade.php                 # Landing page
│   │   │   ├── privacy.blade.php               # Privacy policy
│   │   │   ├── terms.blade.php                 # Terms of service
│   │   │   └── support.blade.php               # Support page
│   │   ├── dashboard/
│   │   │   └── index.blade.php                 # User dashboard
│   │   ├── admin/
│   │   │   ├── dashboard.blade.php             # Admin dashboard
│   │   │   ├── users.blade.php                 # User management
│   │   │   └── transactions.blade.php          # Transaction management
│   │   └── auth/
│   │       ├── login.blade.php                 # Login page
│   │       └── register.blade.php              # Registration
│   ├── js/
│   │   ├── app.js                              # Main JS file
│   │   ├── dashboard.js                        # Dashboard functionality
│   │   └── admin.js                            # Admin panel JS
│   └── css/
│       ├── app.css                             # Main styles
│       ├── dashboard.css                       # Dashboard styles
│       └── admin.css                           # Admin styles
├── routes/
│   ├── web.php                                 # Web routes
│   ├── api.php                                 # API routes for extension
│   └── admin.php                               # Admin routes
├── config/
│   ├── services.php                            # OAuth config
│   ├── payment.php                             # Payment config
│   └── cors.php                                # CORS config
├── public/
│   ├── css/
│   ├── js/
│   └── images/
├── .env                                        # Environment variables
├── composer.json                               # PHP dependencies
└── package.json                                # Frontend dependencies
```

## Migration Steps

### Step 1: Create Laravel Project
```bash
composer create-project laravel/laravel irctc-laravel
cd irctc-laravel
```

### Step 2: Install Required Packages
```bash
# Authentication & API
composer require laravel/sanctum
composer require laravel/socialite

# Payment
composer require razorpay/razorpay

# Admin Panel (Optional - choose one)
composer require laravel/nova  # Premium
# OR
composer require filament/filament  # Free

# Frontend
npm install
npm install axios
```

### Step 3: Database Migration
- Convert MongoDB schemas to Laravel migrations
- Set up relationships using Eloquent
- Migrate existing data

### Step 4: API Routes Mapping

#### Current Node.js → New Laravel API Routes

| Current Node.js Route | New Laravel Route | Controller Method |
|----------------------|-------------------|-------------------|
| `POST /api/auth/google` | `POST /api/auth/google` | `AuthController@googleLogin` |
| `POST /api/auth/facebook` | `POST /api/auth/facebook` | `AuthController@facebookLogin` |
| `GET /api/tickets/count` | `GET /api/user/credits` | `UserController@getCredits` |
| `POST /api/tickets/purchase` | `POST /api/tickets/purchase` | `TicketController@purchase` |
| `GET /api/tickets/booked` | `GET /api/tickets/booked` | `TicketController@getBooked` |
| `POST /api/payment/create-order` | `POST /api/payment/create-order` | `PaymentController@createOrder` |
| `POST /api/payment/verify` | `POST /api/payment/verify` | `PaymentController@verifyPayment` |
| `GET /api/admin/users` | `GET /api/admin/users` | `AdminController@getUsers` |

### Step 5: Web Routes for Website

```php
// routes/web.php
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/privacy', [HomeController::class, 'privacy'])->name('privacy');
Route::get('/terms', [HomeController::class, 'terms'])->name('terms');
Route::get('/support', [HomeController::class, 'support'])->name('support');

// Authentication routes
Route::get('/auth/google', [GoogleController::class, 'redirect']);
Route::get('/auth/google/callback', [GoogleController::class, 'callback']);
Route::get('/auth/facebook', [FacebookController::class, 'redirect']);
Route::get('/auth/facebook/callback', [FacebookController::class, 'callback']);

// Protected routes
Route::middleware('auth')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
});

// Admin routes
Route::prefix('admin')->middleware('admin')->group(function () {
    Route::get('/', [AdminController::class, 'dashboard'])->name('admin.dashboard');
    Route::get('/users', [AdminController::class, 'users'])->name('admin.users');
    Route::get('/transactions', [AdminController::class, 'transactions'])->name('admin.transactions');
});
```

## Chrome Extension Updates

### Update Extension API Calls
```javascript
// Change from Node.js endpoints to Laravel endpoints
const API_BASE = 'http://localhost:8000/api';  // Laravel default port

// Update manifest.json
"host_permissions": [
    "http://localhost:8000/*",  // Laravel development
    "https://yourdomain.com/*", // Production Laravel
    // ... other permissions
]
```

## Advantages of Laravel Migration

1. **Single Codebase**: Web + API in one project
2. **Better Structure**: MVC pattern, service classes
3. **Built-in Features**: Authentication, validation, caching
4. **Easier Maintenance**: One deployment, one database
5. **Better Admin Panel**: Laravel Nova/Filament vs custom React
6. **Robust ORM**: Eloquent vs Mongoose
7. **Better Testing**: PHPUnit integration
8. **Easier Scaling**: Laravel's ecosystem

## Timeline Estimate

- **Week 1**: Laravel setup + Database migration
- **Week 2**: API endpoints + Authentication
- **Week 3**: Web interface + Admin panel
- **Week 4**: Payment integration + Testing
- **Week 5**: Chrome extension updates + Deployment

Would you like me to start creating the Laravel project structure?
