<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Node.js API Endpoints</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container py-5">
        <h1 class="mb-4">Node.js API Test Suite</h1>
        <p class="lead">Test your Node.js backend API endpoints for the IRCTC Chrome Extension</p>
        
        <!-- Server Status -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-server me-2"></i>Server Status</h5>
            </div>
            <div class="card-body">
                <button class="btn btn-primary" onclick="testServerHealth()">Test Node.js Server (Port 3000)</button>
                <div id="server-result" class="mt-3"></div>
            </div>
        </div>

        <!-- API Endpoints -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-plug me-2"></i>API Endpoints</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <button class="btn btn-outline-primary w-100" onclick="testAuthEndpoints()">
                            Test Auth Endpoints
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-outline-info w-100" onclick="testTicketEndpoints()">
                            Test Ticket Endpoints
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-outline-success w-100" onclick="testPaymentEndpoints()">
                            Test Payment Endpoints
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-outline-warning w-100" onclick="testUserEndpoints()">
                            Test User Endpoints
                        </button>
                    </div>
                </div>
                <div id="api-result" class="mt-3"></div>
            </div>
        </div>

        <!-- Chrome Extension Test -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-puzzle-piece me-2"></i>Chrome Extension Test</h5>
            </div>
            <div class="card-body">
                <p>To test the Chrome extension:</p>
                <ol>
                    <li>Make sure your Node.js server is running on <code>http://localhost:3000</code></li>
                    <li>Load the extension in Chrome (Developer mode)</li>
                    <li>Click the extension icon to test authentication</li>
                    <li>Test the dashboard and booking functionality</li>
                </ol>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Extension Location:</strong> <code>irctc-tatkal/</code> folder
                </div>
            </div>
        </div>

        <!-- Required Backend Routes -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-list me-2"></i>Required Backend Routes</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Authentication</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item"><code>POST /api/auth/google</code></li>
                            <li class="list-group-item"><code>POST /api/auth/facebook</code></li>
                        </ul>
                        
                        <h6 class="mt-3">User Management</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item"><code>GET /api/users/credits</code></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Ticket Management</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item"><code>GET /api/tickets/count</code></li>
                            <li class="list-group-item"><code>GET /api/tickets/booked</code></li>
                            <li class="list-group-item"><code>POST /api/tickets/book</code></li>
                            <li class="list-group-item"><code>GET /api/tickets/:id</code></li>
                            <li class="list-group-item"><code>POST /api/tickets/cancel/:id</code></li>
                        </ul>
                        
                        <h6 class="mt-3">Payment</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item"><code>POST /api/payment/create-order</code></li>
                            <li class="list-group-item"><code>POST /api/payment/verify</code></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const API_BASE = 'http://localhost:3000';

        function showResult(containerId, status, message, data = null) {
            const container = document.getElementById(containerId);
            const alertClass = status === 'success' ? 'alert-success' : 
                              status === 'warning' ? 'alert-warning' : 'alert-danger';
            
            let html = `
                <div class="alert ${alertClass}">
                    <strong>${status.toUpperCase()}:</strong> ${message}
                </div>
            `;
            
            if (data) {
                html += `
                    <div class="card">
                        <div class="card-body">
                            <h6>Response:</h6>
                            <pre class="bg-light p-3 rounded"><code>${JSON.stringify(data, null, 2)}</code></pre>
                        </div>
                    </div>
                `;
            }
            
            container.innerHTML = html;
        }

        async function testServerHealth() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('server-result', 'success', 'Node.js server is running!', data);
                } else {
                    showResult('server-result', 'warning', `Server responded with status ${response.status}`);
                }
            } catch (error) {
                showResult('server-result', 'error', `Cannot connect to Node.js server: ${error.message}`);
            }
        }

        async function testAuthEndpoints() {
            let results = [];
            
            // Test Google auth endpoint
            try {
                const response = await fetch(`${API_BASE}/api/auth/google`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ test: true })
                });
                results.push(`Google Auth: ${response.status === 400 || response.status === 200 ? 'Available' : 'Not Available'}`);
            } catch (error) {
                results.push(`Google Auth: Error - ${error.message}`);
            }

            // Test Facebook auth endpoint
            try {
                const response = await fetch(`${API_BASE}/api/auth/facebook`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ test: true })
                });
                results.push(`Facebook Auth: ${response.status === 400 || response.status === 200 ? 'Available' : 'Not Available'}`);
            } catch (error) {
                results.push(`Facebook Auth: Error - ${error.message}`);
            }

            showResult('api-result', 'info', 'Auth Endpoints Test Results', { results });
        }

        async function testTicketEndpoints() {
            let results = [];
            
            const endpoints = [
                { method: 'GET', path: '/api/tickets/count' },
                { method: 'GET', path: '/api/tickets/booked' },
                { method: 'POST', path: '/api/tickets/book' },
                { method: 'GET', path: '/api/tickets/123' },
                { method: 'POST', path: '/api/tickets/cancel/123' }
            ];

            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(`${API_BASE}${endpoint.path}`, {
                        method: endpoint.method,
                        headers: { 
                            'Content-Type': 'application/json',
                            'Authorization': 'Bearer test-token'
                        }
                    });
                    results.push(`${endpoint.method} ${endpoint.path}: ${response.status === 401 || response.status === 200 ? 'Available' : 'Not Available'}`);
                } catch (error) {
                    results.push(`${endpoint.method} ${endpoint.path}: Error - ${error.message}`);
                }
            }

            showResult('api-result', 'info', 'Ticket Endpoints Test Results', { results });
        }

        async function testPaymentEndpoints() {
            let results = [];
            
            const endpoints = [
                { method: 'POST', path: '/api/payment/create-order' },
                { method: 'POST', path: '/api/payment/verify' }
            ];

            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(`${API_BASE}${endpoint.path}`, {
                        method: endpoint.method,
                        headers: { 
                            'Content-Type': 'application/json',
                            'Authorization': 'Bearer test-token'
                        },
                        body: JSON.stringify({ test: true })
                    });
                    results.push(`${endpoint.method} ${endpoint.path}: ${response.status === 400 || response.status === 200 ? 'Available' : 'Not Available'}`);
                } catch (error) {
                    results.push(`${endpoint.method} ${endpoint.path}: Error - ${error.message}`);
                }
            }

            showResult('api-result', 'info', 'Payment Endpoints Test Results', { results });
        }

        async function testUserEndpoints() {
            let results = [];
            
            try {
                const response = await fetch(`${API_BASE}/api/users/credits`, {
                    headers: { 'Authorization': 'Bearer test-token' }
                });
                results.push(`GET /api/users/credits: ${response.status === 401 || response.status === 200 ? 'Available' : 'Not Available'}`);
            } catch (error) {
                results.push(`GET /api/users/credits: Error - ${error.message}`);
            }

            showResult('api-result', 'info', 'User Endpoints Test Results', { results });
        }

        // Auto-test server on page load
        document.addEventListener('DOMContentLoaded', function() {
            testServerHealth();
        });
    </script>
</body>
</html>
