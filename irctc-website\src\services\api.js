import axios from 'axios';

// Safe localStorage access functions
const safeLocalStorageGet = (key) => {
  try {
    if (typeof window !== 'undefined' && window.localStorage) {
      return localStorage.getItem(key);
    }
  } catch (error) {
    console.warn('localStorage access failed:', error);
  }
  return null;
};

// eslint-disable-next-line no-unused-vars
const safeLocalStorageSet = (key, value) => {
  try {
    if (typeof window !== 'undefined' && window.localStorage) {
      localStorage.setItem(key, value);
      return true;
    }
  } catch (error) {
    console.warn('localStorage set failed:', error);
  }
  return false;
};

const safeLocalStorageRemove = (key) => {
  try {
    if (typeof window !== 'undefined' && window.localStorage) {
      localStorage.removeItem(key);
      return true;
    }
  } catch (error) {
    console.warn('localStorage remove failed:', error);
  }
  return false;
};

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:3000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = safeLocalStorageGet('authToken') || safeLocalStorageGet('adminToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Clear tokens on unauthorized
      safeLocalStorageRemove('authToken');
      safeLocalStorageRemove('adminToken');
      safeLocalStorageRemove('user');
      safeLocalStorageRemove('admin');

      // Redirect to login if not already there
      if (typeof window !== 'undefined' && !window.location.pathname.includes('/login') && !window.location.pathname.includes('/admin/login')) {
        window.location.href = '/';
      }
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  // User authentication
  googleLogin: (userData) => api.post('/auth/google', userData),
  facebookLogin: (userData) => api.post('/auth/facebook', userData),

  // Admin authentication
  adminLogin: (credentials) => api.post('/admin/login', credentials),
};

// User API
export const userAPI = {
  getProfile: () => api.get('/users/profile'),
  updatePreferences: (preferences) => api.patch('/users/preferences', preferences),
};

// Tickets API
export const ticketsAPI = {
  getCount: () => api.get('/tickets/count'),
  purchase: (quantity) => api.post('/tickets/purchase', { quantity }),
  getBooked: () => api.get('/tickets/booked'),
  getTicket: (id) => api.get(`/tickets/${id}`),
  book: (ticketData) => api.post('/tickets/book', ticketData),
  cancel: (ticketId) => api.post(`/tickets/cancel/${ticketId}`),
};

// Admin API
export const adminAPI = {
  getStats: () => api.get('/admin/stats'),
  getUsers: (params = {}) => api.get('/admin/users', { params }),
  getUser: (userId) => api.get(`/admin/users/${userId}`),
  updateUser: (userId, updates) => api.patch(`/admin/users/${userId}`, updates),
  updateUserStatus: (userId, status) => api.patch(`/admin/users/${userId}/status`, { status }),
  addCredits: (userId, credits) => api.patch(`/admin/users/${userId}`, {
    availableTickets: credits,
    action: 'add'
  }),
};

// Payment API (placeholder for future implementation)
export const paymentAPI = {
  createOrder: (amount, currency = 'INR') => api.post('/payment/create-order', { amount, currency }),
  verifyPayment: (paymentData) => api.post('/payment/verify', paymentData),
  getTransactions: (userId) => api.get(`/payment/transactions/${userId}`),
};

// Utility functions
export const handleApiError = (error) => {
  if (error.response) {
    // Server responded with error status
    return error.response.data?.message || error.response.data?.error || 'Server error occurred';
  } else if (error.request) {
    // Request was made but no response received
    return 'Network error. Please check your connection.';
  } else {
    // Something else happened
    return error.message || 'An unexpected error occurred';
  }
};

export const isAuthenticated = () => {
  return !!safeLocalStorageGet('authToken');
};

export const isAdminAuthenticated = () => {
  return !!safeLocalStorageGet('adminToken');
};

export const getCurrentUser = () => {
  try {
    const userStr = safeLocalStorageGet('user');
    return userStr ? JSON.parse(userStr) : null;
  } catch (error) {
    console.warn('Failed to parse user data:', error);
    return null;
  }
};

export const getCurrentAdmin = () => {
  try {
    const adminStr = safeLocalStorageGet('admin');
    return adminStr ? JSON.parse(adminStr) : null;
  } catch (error) {
    console.warn('Failed to parse admin data:', error);
    return null;
  }
};

export const logout = () => {
  safeLocalStorageRemove('authToken');
  safeLocalStorageRemove('user');
  if (typeof window !== 'undefined') {
    window.location.href = '/';
  }
};

export const adminLogout = () => {
  safeLocalStorageRemove('adminToken');
  safeLocalStorageRemove('admin');
  if (typeof window !== 'undefined') {
    window.location.href = '/admin/login';
  }
};

export default api;
