<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Render API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #ccc;
        }
        .success {
            background-color: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Render API Test</h1>
        <p>Testing your IRCTC backend deployed on Render: <strong>https://ex-irctc.onrender.com</strong></p>
        
        <div class="test-result info">
            <strong>📋 Test Plan:</strong>
            <ul>
                <li>✅ Root endpoint (/)</li>
                <li>✅ Health check (/health)</li>
                <li>✅ API base (/api)</li>
                <li>✅ API endpoints (/api/auth, /api/users, etc.)</li>
            </ul>
        </div>

        <button onclick="runAllTests()">🚀 Run All Tests</button>
        <button onclick="clearResults()">🧹 Clear Results</button>

        <div id="results"></div>
    </div>

    <script>
        const API_BASE = 'https://ex-irctc.onrender.com';
        const resultsDiv = document.getElementById('results');

        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            resultsDiv.appendChild(div);
        }

        function clearResults() {
            resultsDiv.innerHTML = '';
        }

        async function testEndpoint(url, description) {
            try {
                addResult(`🔄 Testing: ${description} - ${url}`, 'info');
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    addResult(`✅ SUCCESS: ${description}<br><pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
                } else {
                    addResult(`❌ ERROR: ${description}<br>Status: ${response.status}<br><pre>${JSON.stringify(data, null, 2)}</pre>`, 'error');
                }
            } catch (error) {
                addResult(`💥 NETWORK ERROR: ${description}<br>Error: ${error.message}`, 'error');
            }
        }

        async function runAllTests() {
            clearResults();
            addResult('🚀 Starting API tests...', 'info');

            // Test endpoints
            const tests = [
                { url: `${API_BASE}/`, description: 'Root endpoint' },
                { url: `${API_BASE}/health`, description: 'Health check' },
                { url: `${API_BASE}/api`, description: 'API base' },
                { url: `${API_BASE}/api/auth`, description: 'Auth routes (should fail)' },
                { url: `${API_BASE}/api/users`, description: 'Users routes (should fail)' },
                { url: `${API_BASE}/api/tickets`, description: 'Tickets routes (should fail)' },
                { url: `${API_BASE}/api/payment`, description: 'Payment routes (should fail)' }
            ];

            for (const test of tests) {
                await testEndpoint(test.url, test.description);
                // Add small delay between requests
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            addResult('🏁 All tests completed!', 'info');
        }

        // Auto-run tests when page loads
        window.onload = () => {
            addResult('📡 Ready to test your Render deployment!', 'info');
        };
    </script>
</body>
</html>
