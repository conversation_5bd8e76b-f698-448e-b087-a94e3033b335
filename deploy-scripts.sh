#!/bin/bash

# IRCTC Auto Booking - Deployment Scripts
# Usage: ./deploy-scripts.sh [command]

case "$1" in
  "dev")
    echo "🔧 Setting up DEVELOPMENT environment..."
    
    # Backend - use development settings
    cd irctc-backend
    echo "Backend: Using development environment"
    
    # Frontend - use development settings  
    cd ../irctc-website
    echo "Frontend: Using .env.development"
    
    # Chrome Extension - build for development
    cd ../irctc-tatkal
    npm run build:dev
    echo "✅ Chrome Extension: Built for development"
    
    echo "🎉 Development environment ready!"
    echo "📝 Next steps:"
    echo "   1. cd irctc-backend && npm run dev"
    echo "   2. cd irctc-website && npm start"
    echo "   3. Load Chrome extension from irctc-tatkal folder"
    ;;
    
  "prod")
    echo "🚀 Setting up PRODUCTION environment..."
    
    # Frontend - build for production
    cd irctc-website
    echo "Frontend: Building for production..."
    npm run build
    echo "✅ Frontend: Production build complete"
    
    # Chrome Extension - build for production
    cd ../irctc-tatkal
    npm run build:prod
    npm run package:prod
    echo "✅ Chrome Extension: Production package created"
    
    echo "🎉 Production builds ready!"
    echo "📝 Next steps:"
    echo "   1. Deploy irctc-website/build to Netlify"
    echo "   2. Deploy irctc-backend to Render"
    echo "   3. Upload irctc-extension-prod.zip to Chrome Web Store"
    ;;
    
  "test")
    echo "🧪 Testing all environments..."
    
    # Test backend health
    echo "Testing backend..."
    curl -s http://localhost:3000/health || echo "❌ Backend not running"
    
    # Test frontend
    echo "Testing frontend..."
    curl -s http://localhost:3001 > /dev/null && echo "✅ Frontend running" || echo "❌ Frontend not running"
    
    echo "🔍 Check browser console for Chrome extension status"
    ;;
    
  "clean")
    echo "🧹 Cleaning build artifacts..."
    
    # Clean frontend build
    cd irctc-website
    rm -rf build
    rm -rf node_modules/.cache
    echo "✅ Frontend: Cleaned"
    
    # Clean extension builds
    cd ../irctc-tatkal
    rm -f *.zip
    echo "✅ Extension: Cleaned"
    
    echo "🎉 Cleanup complete!"
    ;;
    
  *)
    echo "🚀 IRCTC Auto Booking - Deployment Scripts"
    echo ""
    echo "Usage: ./deploy-scripts.sh [command]"
    echo ""
    echo "Commands:"
    echo "  dev     - Setup development environment"
    echo "  prod    - Build for production deployment"
    echo "  test    - Test current environment"
    echo "  clean   - Clean build artifacts"
    echo ""
    echo "Examples:"
    echo "  ./deploy-scripts.sh dev    # Setup for local development"
    echo "  ./deploy-scripts.sh prod   # Build for production"
    ;;
esac
