require('dotenv').config();
const jwt = require('jsonwebtoken');
const mongoose = require('mongoose');
const connectDB = require('./config/db');

// Connect to MongoDB
connectDB();

// Create a test token
const createToken = () => {
    // Create a token with id field (for admin)
    const adminToken = jwt.sign(
        { id: '123456789012345678901234' }, // Mock ObjectId
        process.env.JWT_SECRET || 'your-secret-key-here',
        { expiresIn: '1h' }
    );
    
    // Create a token with userId field (for user)
    const userToken = jwt.sign(
        { userId: '123456789012345678901234' }, // Mock ObjectId
        process.env.JWT_SECRET || 'your-secret-key-here',
        { expiresIn: '1h' }
    );
    
    console.log('Admin Token:', adminToken);
    console.log('User Token:', userToken);
    
    // Verify the tokens
    try {
        const decodedAdmin = jwt.verify(adminToken, process.env.JWT_SECRET || 'your-secret-key-here');
        console.log('Decoded Admin Token:', decodedAdmin);
        
        const decodedUser = jwt.verify(userToken, process.env.JWT_SECRET || 'your-secret-key-here');
        console.log('Decoded User Token:', decodedUser);
    } catch (error) {
        console.error('Token verification error:', error);
    }
};

createToken();

// Close the connection after 2 seconds
setTimeout(() => {
    mongoose.connection.close();
    console.log('Database connection closed');
}, 2000);
