<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Payment Integration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Payment Integration Test</h1>
    
    <div class="test-section info">
        <h3>Backend Server Status</h3>
        <button onclick="testServerHealth()">Test Server Health</button>
        <div id="server-result" class="result"></div>
    </div>

    <div class="test-section info">
        <h3>Payment Service Test</h3>
        <button onclick="testPaymentService()">Test Payment Service</button>
        <div id="payment-result" class="result"></div>
    </div>

    <div class="test-section info">
        <h3>Credits API Test</h3>
        <button onclick="testCreditsAPI()">Test Credits API (requires auth)</button>
        <div id="credits-result" class="result"></div>
    </div>

    <div class="test-section info">
        <h3>Create Order Test</h3>
        <button onclick="testCreateOrder()">Test Create Order (requires auth)</button>
        <div id="order-result" class="result"></div>
    </div>

    <script>
        async function testServerHealth() {
            const resultDiv = document.getElementById('server-result');
            resultDiv.textContent = 'Testing...';
            
            try {
                const response = await fetch('http://localhost:3000/health');
                const data = await response.json();
                
                resultDiv.textContent = `Status: ${response.status}\nResponse: ${JSON.stringify(data, null, 2)}`;
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = `Error: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        async function testPaymentService() {
            const resultDiv = document.getElementById('payment-result');
            resultDiv.textContent = 'Testing...';
            
            try {
                const response = await fetch('http://localhost:3000/api/payment/test');
                const data = await response.json();
                
                resultDiv.textContent = `Status: ${response.status}\nResponse: ${JSON.stringify(data, null, 2)}`;
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = `Error: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        async function testCreditsAPI() {
            const resultDiv = document.getElementById('credits-result');
            resultDiv.textContent = 'Testing...';
            
            try {
                const response = await fetch('http://localhost:3000/api/users/credits');
                const data = await response.json();
                
                resultDiv.textContent = `Status: ${response.status}\nResponse: ${JSON.stringify(data, null, 2)}`;
                resultDiv.className = response.status === 401 ? 'result info' : 'result success';
            } catch (error) {
                resultDiv.textContent = `Error: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        async function testCreateOrder() {
            const resultDiv = document.getElementById('order-result');
            resultDiv.textContent = 'Testing...';
            
            try {
                const response = await fetch('http://localhost:3000/api/payment/create-order', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        amount: 50000, // 500 rupees in paise
                        currency: 'INR'
                    })
                });
                const data = await response.json();
                
                resultDiv.textContent = `Status: ${response.status}\nResponse: ${JSON.stringify(data, null, 2)}`;
                resultDiv.className = response.status === 401 ? 'result info' : 'result success';
            } catch (error) {
                resultDiv.textContent = `Error: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
    </script>
</body>
</html>
