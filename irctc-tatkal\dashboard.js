document.addEventListener('DOMContentLoaded', async function() {
    try {
        // Check if user is logged in
        const result = await new Promise(resolve => {
            chrome.storage.local.get(['userInfo', 'authToken'], resolve);
        });

        console.log('Auth token exists:', !!result.authToken);
        console.log('Auth token prefix:', result.authToken ? result.authToken.substring(0, 10) + '...' : 'none');

        if (!result.userInfo || !result.authToken) {
            console.log('No user info or auth token found, redirecting to login');
            window.location.href = 'popup.html';
            return;
        }

        // Update user info in the UI
        document.getElementById('user-name').textContent = result.userInfo.name;
        document.getElementById('user-email').textContent = result.userInfo.email;
        document.getElementById('user-avatar').src = result.userInfo.picture || 'images/default-avatar.png';

        // Initialize the dashboard
        initializeDashboard(result.authToken);

    } catch (error) {
        console.error('Initialization error:', error);
        showError('Failed to initialize dashboard. Please try again.');
    }
});

// Initialize all dashboard components
async function initializeDashboard(authToken) {
    try {
        // Fetch available tickets count
        await fetchTicketsCount(authToken);

        // Check if booked tickets section exists before fetching
        if (document.querySelector('.booked-tickets-section:not([style*="display: none"])')) {
            // Fetch booked tickets
            await fetchBookedTickets(authToken);
        }

        // Fetch saved tickets
        console.log('Initializing saved tickets section...');
        fetchSavedTickets();
        // Set up event listeners
        setupEventListeners(authToken);

        // Set up periodic refresh
        setupPeriodicRefresh(authToken);

    } catch (error) {
        console.error('Dashboard initialization error:', error);
        showError('Failed to load dashboard data. Please try again.');
    }
}
// Set up all event listeners
function setupEventListeners(authToken) {
    // Handle Add Tickets button
    document.getElementById('add-tickets-btn').addEventListener('click', function() {
        // Redirect to add-credits page
        window.location.href = 'add-credits.html';
    });

    // Handle Detailed Booking button
    document.getElementById('detailed-booking-btn').addEventListener('click', function(e) {
        e.preventDefault();
            chrome.tabs.create({
                url: chrome.runtime.getURL('book-ticket.html')
            });
        // Save form data to use in the detailed booking page
        // const formData = {
        //     fromStation: document.getElementById('from-station').value,
        //     toStation: document.getElementById('to-station').value,
        //     journeyDate: document.getElementById('journey-date').value,
        //     class: document.getElementById('class').value
        // };

        // Save as draft
        // chrome.storage.local.set({ 'bookingDraft': formData }, function() {
        //     console.log('Basic form data saved for detailed booking');

        //     // Open detailed booking page in a new tab to make it persistent
        //     chrome.tabs.create({
        //         url: chrome.runtime.getURL('book-ticket.html')
        //     });
        // });
    });

    // Handle Refresh Saved Tickets button
    const refreshSavedTicketsBtn = document.getElementById('refresh-saved-tickets-btn');
    if (refreshSavedTicketsBtn) {
        refreshSavedTicketsBtn.addEventListener('click', function() {
            console.log('Manually refreshing saved tickets...');
            fetchSavedTickets();
        });
    }

    // Handle booking form submission
    // document.getElementById('booking-form').addEventListener('submit', async function(e) {
    //     e.preventDefault();

    //     const formData = {
    //         fromStation: document.getElementById('from-station').value,
    //         toStation: document.getElementById('to-station').value,
    //         journeyDate: document.getElementById('journey-date').value,
    //         class: document.getElementById('class').value
    //     };

    //     try {
    //         // Check if user has available booking credits
    //         const ticketsCount = await fetchTicketsCount(authToken);
    //         if (ticketsCount <= 0) {
    //             alert('You have no booking credits available. Please purchase credits first to book a train ticket.');
    //             document.querySelector('.payment-section').scrollIntoView({ behavior: 'smooth' });
    //             return;
    //         }

    //         // Book the ticket through our API
    //         const response = await fetch('http://localhost:3000/api/tickets/book', {
    //             method: 'POST',
    //             headers: {
    //                 'Authorization': `Bearer ${authToken}`,
    //                 'Content-Type': 'application/json'
    //             },
    //             body: JSON.stringify(formData)
    //         });

    //         const data = await response.json();

    //         if (!response.ok) {
    //             throw new Error(data.message || 'Failed to book ticket');
    //         }

    //         // Update tickets count
    //         document.getElementById('tickets-count').textContent = data.availableTickets;

    //         // Refresh booked tickets
    //         await fetchBookedTickets(authToken);

    //         // Show success message
    //         alert('Ticket booked successfully! PNR: ' + data.ticket.pnr);

    //         // Open IRCTC booking page with pre-filled data
    //         chrome.tabs.create({
    //             url: `https://www.irctc.co.in/nget/booking/train-list?fromStation=${encodeURIComponent(formData.fromStation)}&toStation=${encodeURIComponent(formData.toStation)}&journeyDate=${formData.journeyDate}&classCode=${formData.class}`
    //         });

    //     } catch (error) {
    //         console.error('Booking error:', error);
    //         alert('Booking failed: ' + error.message);
    //     }
    // });

    // Handle payment options
    document.querySelectorAll('.payment-option').forEach(option => {
        option.addEventListener('click', async function() {
            const quantity = parseInt(this.dataset.quantity);

            // Open payment page in new tab
            chrome.tabs.create({
                url: `http://localhost:3000/payment?quantity=${quantity}&return_url=${encodeURIComponent(chrome.runtime.getURL('dashboard.html'))}`
            });
        });
    });

    // Handle logout
    document.getElementById('logout-btn').addEventListener('click', async () => {
        try {
            const token = await new Promise((resolve, reject) => {
                chrome.identity.getAuthToken({ interactive: false }, (token) => {
                    if (chrome.runtime.lastError) {
                        reject(chrome.runtime.lastError);
                        return;
                    }
                    resolve(token);
                });
            });

            if (token) {
                await new Promise((resolve, reject) => {
                    chrome.identity.removeCachedAuthToken({ token }, () => {
                        if (chrome.runtime.lastError) {
                            reject(chrome.runtime.lastError);
                            return;
                        }
                        resolve();
                    });
                });

                await fetch(`https://accounts.google.com/o/oauth2/revoke?token=${token}`);
            }

            await new Promise((resolve) => {
                chrome.storage.local.clear(() => {
                    resolve();
                });
            });

            window.location.href = 'popup.html';
        } catch (error) {
            console.error('Logout failed:', error);
            alert('Logout failed: ' + error.message);
        }
    });
}

// Show loading state for booking credits count
function setTicketsLoadingState(isLoading) {
    const ticketsElement = document.getElementById('tickets-count');
    if (isLoading) {
        ticketsElement.textContent = '...';
        ticketsElement.classList.add('loading');
    } else {
        ticketsElement.classList.remove('loading');
    }
}

// Fetch available booking credits count
async function fetchTicketsCount(authToken) {
    if (!authToken) {
        console.error('No auth token provided');
        return 0;
    }

    setTicketsLoadingState(true);

    // Remove any existing error messages first
    const existingErrors = document.querySelectorAll('.error-message');
    existingErrors.forEach(error => error.remove());

    try {
        console.log('Fetching booking credits count...');
        const response = await fetch('http://localhost:3000/api/tickets/count', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });

        console.log('Response status:', response.status);
        const data = await response.json();
        console.log('Response data:', data);

        if (!response.ok) {
            // Check if token expired
            if (response.status === 401) {
                console.log('Token expired, redirecting to login');
                // Clear storage and redirect to login
                await chrome.storage.local.remove(['authToken', 'userInfo', 'isLoggedIn']);
                window.location.href = 'popup.html';
                return 0;
            }
            throw new Error(data.message || 'Failed to fetch booking credits count');
        }

        const ticketsElement = document.getElementById('tickets-count');
        if (ticketsElement) {
            ticketsElement.textContent = data.count || 0;
        }
        return data.count || 0;

    } catch (error) {
        console.error('Error fetching tickets count:', error);

        // Update the tickets count display
        const ticketsElement = document.getElementById('tickets-count');
        if (ticketsElement) {
            ticketsElement.textContent = '0';
        }

        // Show error message
        showError('Failed to load booking credits count');

        return 0;
    } finally {
        setTicketsLoadingState(false);
    }
}

// Fetch train bookings
async function fetchBookedTickets(authToken) {
    if (!authToken) {
        console.error('No auth token provided');
        return [];
    }

    const container = document.getElementById('booked-tickets-container');
    const noTicketsMessage = document.getElementById('no-tickets-message');

    // Show loading state
    container.innerHTML = '<div class="loading-message">Loading your train bookings...</div>';
    noTicketsMessage.style.display = 'none';

    try {
        console.log('Fetching train bookings...');
        const response = await fetch('http://localhost:3000/api/tickets/booked', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });

        console.log('Response status:', response.status);
        const data = await response.json();
        console.log('Response data:', data);

        if (!response.ok) {
            // Check if token expired
            if (response.status === 401) {
                console.log('Token expired, redirecting to login');
                // Clear storage and redirect to login
                await chrome.storage.local.remove(['authToken', 'userInfo', 'isLoggedIn']);
                window.location.href = 'popup.html';
                return [];
            }
            throw new Error(data.message || 'Failed to fetch train bookings');
        }

        const tickets = data.tickets || [];

        // Clear container
        container.innerHTML = '';

        if (tickets.length === 0) {
            // Show no tickets message
            noTicketsMessage.style.display = 'block';
            return [];
        }

        // Render tickets
        tickets.forEach(ticket => {
            const ticketCard = createTicketCard(ticket, authToken);
            container.appendChild(ticketCard);
        });

        return tickets;

    } catch (error) {
        console.error('Error fetching train bookings:', error);
        container.innerHTML = `<div class="error-message">Failed to load train bookings: ${error.message}</div>`;
        return [];
    }
}

// Create a ticket card element
function createTicketCard(ticket, authToken) {
    const card = document.createElement('div');
    card.className = `ticket-card ${ticket.status}`;

    // Format date
    const journeyDate = new Date(ticket.journeyDate).toLocaleDateString();
    const bookingDate = new Date(ticket.bookingDate).toLocaleDateString();

    // Create card content
    card.innerHTML = `
        <div class="ticket-header">
            <div class="ticket-route">${ticket.fromStation} → ${ticket.toStation}</div>
            <div class="ticket-status ${ticket.status}">${ticket.status}</div>
        </div>
        <div class="ticket-details">
            <div class="ticket-date">Journey: ${journeyDate}</div>
            <div class="ticket-class">Class: ${ticket.class}</div>
            <div class="ticket-pnr">PNR: ${ticket.pnr}</div>
        </div>
        <div class="ticket-booking-date">Booked on: ${bookingDate}</div>
    `;

    // Add actions div
    const actionsDiv = document.createElement('div');
    actionsDiv.className = 'ticket-actions';

    // Add view details button
    const viewDetailsBtn = document.createElement('button');
    viewDetailsBtn.className = 'btn-view-details';
    viewDetailsBtn.innerHTML = '<i class="fas fa-eye"></i> View Details';
    viewDetailsBtn.addEventListener('click', () => viewTicketDetails(ticket._id));
    actionsDiv.appendChild(viewDetailsBtn);

    // Add cancel button for booked tickets
    if (ticket.status === 'booked') {
        const cancelBtn = document.createElement('button');
        cancelBtn.className = 'btn-cancel';
        cancelBtn.innerHTML = '<i class="fas fa-times"></i> Cancel';
        cancelBtn.addEventListener('click', () => cancelTicket(ticket._id, authToken));
        actionsDiv.appendChild(cancelBtn);
    }

    card.appendChild(actionsDiv);
    return card;
}

// View ticket details
function viewTicketDetails(ticketId) {
    // Navigate to ticket details page with ticket ID
    window.location.href = `ticket-details.html?id=${ticketId}`;
}

// Cancel a ticket
async function cancelTicket(ticketId, authToken) {
    if (!confirm('Are you sure you want to cancel this ticket?')) {
        return;
    }

    try {
        const response = await fetch(`http://localhost:3000/api/tickets/cancel/${ticketId}`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.message || 'Failed to cancel ticket');
        }

        // Update tickets count
        document.getElementById('tickets-count').textContent = data.availableTickets;

        // Refresh booked tickets
        await fetchBookedTickets(authToken);

        // Show success message
        alert('Ticket cancelled successfully');

    } catch (error) {
        console.error('Error cancelling ticket:', error);
        alert('Failed to cancel ticket: ' + error.message);
    }
}

// Set up periodic refresh
function setupPeriodicRefresh(authToken) {
    // Refresh booking credits count every 30 seconds
    setInterval(() => {
        fetchTicketsCount(authToken).catch(console.error);
    }, 30000);

    // Refresh train bookings every 60 seconds (if section exists)
    if (document.querySelector('.booked-tickets-section:not([style*="display: none"])')) {
        setInterval(() => {
            fetchBookedTickets(authToken).catch(console.error);
        }, 60000);
    }

    // Refresh saved tickets every 60 seconds
    setInterval(() => {
        fetchSavedTickets();
    }, 60000);
}

// Fetch saved tickets from local storage
function fetchSavedTickets() {
    console.log('Fetching saved tickets from local storage...');
    const container = document.getElementById('saved-tickets-container');
    const noSavedTicketsMessage = document.getElementById('no-saved-tickets-message');

    if (!container) {
        console.error('Saved tickets container not found in the DOM');
        return;
    }

    if (!noSavedTicketsMessage) {
        console.error('No saved tickets message element not found in the DOM');
    }

    // Show loading state
    container.innerHTML = '<div class="loading-message">Loading your saved tickets...</div>';
    if (noSavedTicketsMessage) {
        noSavedTicketsMessage.style.display = 'none';
    }

    try {
        // Get saved tickets from local storage
        chrome.storage.local.get(['savedTickets'], function(result) {
            console.log('Retrieved saved tickets from storage:', result);
            const savedTickets = result.savedTickets || [];

            // Clear container
            container.innerHTML = '';

            if (!savedTickets || savedTickets.length === 0) {
                console.log('No saved tickets found in storage');
                // Show no saved tickets message
                if (noSavedTicketsMessage) {
                    noSavedTicketsMessage.style.display = 'block';
                }
                return;
            }

            console.log(`Found ${savedTickets.length} saved tickets, rendering...`);

            // Render saved tickets
            savedTickets.forEach((ticket, index) => {
                console.log(`Rendering ticket ${index + 1}:`, ticket);
                const ticketCard = createSavedTicketCard(ticket, index);
                container.appendChild(ticketCard);
            });
        });
    } catch (error) {
        console.error('Error fetching saved tickets:', error);
        container.innerHTML = `<div class="error-message">Failed to load saved tickets: ${error.message}</div>`;
    }
}

// Create a saved ticket card element
function createSavedTicketCard(ticket, index) {
    console.log('Creating card for ticket:', ticket);
    const card = document.createElement('div');
    card.className = 'ticket-card saved-ticket';

    try {
        // Format date
        let journeyDate = 'Unknown date';
        if (ticket.journeyDate) {
            try {
                journeyDate = new Date(ticket.journeyDate).toLocaleDateString();
            } catch (e) {
                console.error('Error formatting journey date:', e);
            }
        }

        // Extract station names (removing station codes if present)
        let fromStation = 'Unknown';
        let toStation = 'Unknown';

        if (ticket.fromStation) {
            try {
                fromStation = ticket.fromStation.split(' - ')[0] || ticket.fromStation;
            } catch (e) {
                console.error('Error extracting from station:', e);
                fromStation = ticket.fromStation;
            }
        }

        if (ticket.toStation) {
            try {
                toStation = ticket.toStation.split(' - ')[0] || ticket.toStation;
            } catch (e) {
                console.error('Error extracting to station:', e);
                toStation = ticket.toStation;
            }
        }

        // Create card content
        card.innerHTML = `
            <div class="ticket-header">
                <div class="ticket-route">${fromStation} → ${toStation}</div>
                <div class="ticket-train">${ticket.trainNumber || ''}</div>
            </div>
            <div class="ticket-details">
                <div class="ticket-date">Journey: ${journeyDate}</div>
                <div class="ticket-class">Class: ${ticket.class || 'Unknown'}</div>
            </div>
        `;
    } catch (error) {
        console.error('Error creating ticket card:', error);
        card.innerHTML = `
            <div class="ticket-header">
                <div class="ticket-route">Error displaying ticket</div>
            </div>
            <div class="ticket-details">
                <div class="ticket-date">Error: ${error.message}</div>
            </div>
        `;
    }

    // Add actions div
    const actionsDiv = document.createElement('div');
    actionsDiv.className = 'ticket-actions';

    // Add re-book button
    const rebookBtn = document.createElement('button');
    rebookBtn.className = 'btn-rebook';
    rebookBtn.innerHTML = '<i class="fas fa-ticket-alt"></i> Re-book';
    rebookBtn.addEventListener('click', () => rebookSavedTicket(ticket));
    actionsDiv.appendChild(rebookBtn);

    // Add edit button
    const editBtn = document.createElement('button');
    editBtn.className = 'btn-edit';
    editBtn.innerHTML = '<i class="fas fa-edit"></i> Edit';
    editBtn.addEventListener('click', () => editSavedTicket(ticket));
    actionsDiv.appendChild(editBtn);

    // Add delete button
    const deleteBtn = document.createElement('button');
    deleteBtn.className = 'btn-delete';
    deleteBtn.innerHTML = '<i class="fas fa-trash"></i> Delete';
    deleteBtn.addEventListener('click', () => deleteSavedTicket(index));
    actionsDiv.appendChild(deleteBtn);

    card.appendChild(actionsDiv);
    return card;
}

// Re-book a saved ticket
function rebookSavedTicket(ticket) {
    try {
        // Store the booking data in chrome.storage for the content script to use
        chrome.storage.local.set({
            'irctc_credentials': {
                user_name: ticket.irctcUsername || '',
                password: ticket.irctcPassword || ''
            },
            'journey_details': {
                from: ticket.fromStation,
                destination: ticket.toStation,
                date: ticket.journeyDate,
                class: ticket.class,
                quota: ticket.quota || 'GN',
                'train-no': ticket.trainNumber || '00000',
                boarding: ticket.boardingStation || ''
            },
            'passenger_details': ticket.passengers || [],
            'infant_details': ticket.infants || [],
            'other_preferences': ticket.otherPreferences || {
                mobileNumber: ticket.mobileNumber || '',
                autoCaptcha: true,
                autoUpgradation: false,
                confirmberths: false,
                paymentmethod: 'UPI',
                acbooktime: '09:59:59',
                slbooktime: '10:59:59',
                gnbooktime: '07:59:59',
                CaptchaSubmitMode: 'A'
            },
            'travel_preferences': ticket.travelPreferences || {
                AvailabilityCheck: 'A',
                travelInsuranceOpted: 'no',
                prefcoach: '',
                reservationchoice: 'LOWER'
            }
        }, function() {
            console.log('Booking data saved for auto-booking');

            // Open IRCTC website in a new tab
            chrome.tabs.create({
                url: 'https://www.irctc.co.in/nget/train-search'
            });
        });
    } catch (error) {
        console.error('Error rebooking saved ticket:', error);
        alert('Failed to rebook ticket: ' + error.message);
    }
}

// Edit a saved ticket
function editSavedTicket(ticket) {
    try {
        // Save as draft to pre-fill the booking form
        chrome.storage.local.set({ 'bookingDraft': ticket }, function() {
            console.log('Saved ticket data prepared for editing');

            // Open detailed booking page in a new tab
            chrome.tabs.create({
                url: chrome.runtime.getURL('book-ticket.html')
            });
        });
    } catch (error) {
        console.error('Error editing saved ticket:', error);
        alert('Failed to edit ticket: ' + error.message);
    }
}

// Delete a saved ticket
function deleteSavedTicket(index) {
    if (!confirm('Are you sure you want to delete this saved ticket?')) {
        return;
    }

    try {
        chrome.storage.local.get(['savedTickets'], function(result) {
            const savedTickets = result.savedTickets || [];

            // Remove the ticket at the specified index
            if (index >= 0 && index < savedTickets.length) {
                savedTickets.splice(index, 1);

                // Save the updated list back to storage
                chrome.storage.local.set({ 'savedTickets': savedTickets }, function() {
                    console.log('Saved ticket deleted');

                    // Refresh the saved tickets display
                    fetchSavedTickets();
                });
            }
        });
    } catch (error) {
        console.error('Error deleting saved ticket:', error);
        alert('Failed to delete ticket: ' + error.message);
    }
}

// Show error message
function showError(message) {
    // Remove any existing error messages first
    const existingErrors = document.querySelectorAll('.error-message');
    existingErrors.forEach(error => error.remove());

    // Create error message element
    const errorMessage = document.createElement('div');
    errorMessage.className = 'error-message';
    errorMessage.style.color = '#d32f2f';
    errorMessage.style.backgroundColor = '#ffebee';
    errorMessage.style.padding = '8px';
    errorMessage.style.borderRadius = '4px';
    errorMessage.style.marginTop = '8px';
    errorMessage.style.fontSize = '14px';
    errorMessage.textContent = message;

    // Add to container
    const container = document.querySelector('.container');
    container.insertBefore(errorMessage, container.firstChild);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        errorMessage.remove();
    }, 5000);
}

