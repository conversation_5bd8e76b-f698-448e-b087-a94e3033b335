<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\PaymentController;
use App\Http\Controllers\Api\TicketController;
use App\Http\Controllers\Api\UserController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Health check
Route::get('/health', function () {
    return response()->json([
        'status' => 'ok',
        'message' => 'IRCTC Laravel API is running',
        'timestamp' => now(),
    ]);
});

// Authentication routes (for Chrome extension)
Route::prefix('auth')->group(function () {
    Route::post('/google', [AuthController::class, 'googleLogin']);
    Route::post('/facebook', [AuthController::class, 'facebookLogin']);
    
    // Protected auth routes
    Route::middleware('auth:sanctum')->group(function () {
        Route::get('/me', [AuthController::class, 'me']);
        Route::post('/logout', [AuthController::class, 'logout']);
    });
});

// User routes (protected)
Route::middleware('auth:sanctum')->prefix('users')->group(function () {
    Route::get('/credits', [UserController::class, 'getCredits']);
    Route::get('/profile', [UserController::class, 'getProfile']);
    Route::post('/preferences', [UserController::class, 'updatePreferences']);
    Route::post('/api-key/regenerate', [UserController::class, 'regenerateApiKey']);
});

// Ticket routes (protected)
Route::middleware('auth:sanctum')->prefix('tickets')->group(function () {
    Route::get('/count', [TicketController::class, 'getCount']);
    Route::post('/purchase', [TicketController::class, 'purchase']);
    Route::get('/booked', [TicketController::class, 'getBooked']);
    Route::get('/{id}', [TicketController::class, 'getTicket']);
    Route::post('/book', [TicketController::class, 'book']);
    Route::post('/{id}/cancel', [TicketController::class, 'cancel']);
});

// Payment routes
Route::prefix('payment')->group(function () {
    Route::get('/test', [PaymentController::class, 'test']);
    
    // Protected payment routes
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/create-order', [PaymentController::class, 'createOrder']);
        Route::post('/verify', [PaymentController::class, 'verifyPayment']);
        Route::get('/transactions', [PaymentController::class, 'getTransactions']);
        Route::get('/transactions/{id}', [PaymentController::class, 'getTransaction']);
    });
});

// Admin API routes (if needed for admin panel)
Route::middleware('auth:sanctum')->prefix('admin')->group(function () {
    // Add admin API routes here if needed
});
