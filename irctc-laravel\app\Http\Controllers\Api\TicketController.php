<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Ticket;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class TicketController extends Controller
{
    /**
     * Get user's available tickets count
     */
    public function getCount(Request $request)
    {
        try {
            $user = $request->user();

            return response()->json([
                'success' => true,
                'count' => $user->available_tickets,
                'message' => 'Tickets count retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch tickets count',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Purchase tickets (add credits)
     */
    public function purchase(Request $request)
    {
        try {
            $request->validate([
                'quantity' => 'required|integer|min:1|max:100'
            ]);

            $user = $request->user();
            $user->increment('available_tickets', $request->quantity);

            return response()->json([
                'success' => true,
                'message' => 'Tickets purchased successfully',
                'new_count' => $user->fresh()->available_tickets
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Purchase failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get user's booked tickets
     */
    public function getBooked(Request $request)
    {
        try {
            $user = $request->user();
            $tickets = $user->tickets()->orderBy('created_at', 'desc')->get();

            return response()->json([
                'success' => true,
                'tickets' => $tickets,
                'message' => 'Booked tickets retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch booked tickets',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a specific ticket by ID
     */
    public function getTicket(Request $request, $id)
    {
        try {
            $user = $request->user();
            $ticket = $user->tickets()->findOrFail($id);

            return response()->json([
                'success' => true,
                'ticket' => [
                    'id' => $ticket->id,
                    'from_station' => $ticket->from_station,
                    'to_station' => $ticket->to_station,
                    'journey_date' => $ticket->journey_date,
                    'class' => $ticket->class,
                    'status' => $ticket->status,
                    'pnr' => $ticket->pnr,
                    'train_number' => $ticket->train_number,
                    'train_name' => $ticket->train_name,
                    'boarding_station' => $ticket->boarding_station,
                    'passengers' => $ticket->passengers,
                    'infants' => $ticket->infants,
                    'mobile_number' => $ticket->mobile_number,
                    'payment_method' => $ticket->payment_method,
                    'transaction_id' => $ticket->transaction_id,
                    'booking_date' => $ticket->booking_date,
                ],
                'message' => 'Ticket details retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ticket not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Book a new ticket
     */
    public function book(Request $request)
    {
        try {
            $request->validate([
                'from_station' => 'required|string',
                'to_station' => 'required|string',
                'journey_date' => 'required|date',
                'class' => 'required|in:1A,2A,3A,SL,CC,EC',
                'train_number' => 'sometimes|string',
                'passengers' => 'sometimes|array',
                'mobile_number' => 'sometimes|string',
            ]);

            $user = $request->user();

            // Check if user has available tickets
            if ($user->available_tickets <= 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'No available tickets'
                ], 400);
            }

            // Create ticket
            $ticket = $user->tickets()->create([
                'from_station' => $request->from_station,
                'to_station' => $request->to_station,
                'journey_date' => $request->journey_date,
                'class' => $request->class,
                'train_number' => $request->train_number,
                'passengers' => $request->passengers,
                'mobile_number' => $request->mobile_number,
                'pnr' => 'PNR' . strtoupper(Str::random(10)), // Generate fake PNR for demo
                'status' => 'booked',
            ]);

            // Decrement available tickets
            $user->decrement('available_tickets');

            return response()->json([
                'success' => true,
                'message' => 'Ticket booked successfully',
                'ticket' => $ticket,
                'available_tickets' => $user->fresh()->available_tickets
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Booking failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cancel a ticket
     */
    public function cancel(Request $request, $ticketId)
    {
        try {
            $user = $request->user();
            $ticket = $user->tickets()->findOrFail($ticketId);

            if ($ticket->status === 'cancelled') {
                return response()->json([
                    'success' => false,
                    'message' => 'Ticket is already cancelled'
                ], 400);
            }

            // Update ticket status
            $ticket->update(['status' => 'cancelled']);

            // Refund the credit
            $user->increment('available_tickets');

            return response()->json([
                'success' => true,
                'message' => 'Ticket cancelled successfully',
                'available_tickets' => $user->fresh()->available_tickets
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel ticket',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
