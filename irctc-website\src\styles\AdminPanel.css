.admin-panel {
  min-height: 100vh;
  background-color: #f8fafc;
}

/* Stats Section */
.stats-section {
  margin-bottom: 40px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.stat-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  flex-shrink: 0;
}

.stat-content h3 {
  margin: 0 0 4px 0;
  color: #1f2937;
  font-size: 28px;
  font-weight: 700;
}

.stat-content p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
}

/* Recent Users Section */
.recent-users-section {
  background: white;
  padding: 30px;
  border-radius: 16px;
  margin-bottom: 40px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.users-table {
  overflow-x: auto;
}

.users-table table {
  width: 100%;
  border-collapse: collapse;
}

.users-table th,
.users-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.users-table th {
  background-color: #f8fafc;
  color: #374151;
  font-weight: 600;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.users-table td {
  color: #1f2937;
  font-size: 14px;
}

.user-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.action-btn {
  padding: 6px 12px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background-color: #2563eb;
}

/* Quick Actions Section */
.quick-actions-section {
  background: white;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.quick-actions-section h2 {
  margin: 0 0 30px 0;
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.action-card {
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  transition: all 0.2s ease;
}

.action-card:hover {
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.action-icon {
  font-size: 40px;
  margin-bottom: 16px;
}

.action-card h3 {
  margin: 0 0 12px 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
}

.action-card p {
  margin: 0 0 20px 0;
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
}

.action-button {
  width: 100%;
  padding: 10px 20px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button:hover {
  background-color: #2563eb;
}

/* Loading State */
.admin-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  gap: 16px;
}

.admin-loading p {
  color: #64748b;
  font-size: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }
  
  .stat-card {
    padding: 20px;
    flex-direction: column;
    text-align: center;
  }
  
  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 24px;
  }
  
  .stat-content h3 {
    font-size: 24px;
  }
  
  .recent-users-section,
  .quick-actions-section {
    padding: 20px;
  }
  
  .users-table {
    font-size: 12px;
  }
  
  .users-table th,
  .users-table td {
    padding: 8px 12px;
  }
  
  .user-cell {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
  
  .actions-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .action-card {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .stat-card {
    padding: 16px;
  }
  
  .users-table th,
  .users-table td {
    padding: 6px 8px;
    font-size: 11px;
  }
  
  .action-icon {
    font-size: 32px;
  }
  
  .action-card h3 {
    font-size: 16px;
  }
  
  .action-card p {
    font-size: 13px;
  }
}

/* Table Responsive Behavior */
@media (max-width: 640px) {
  .users-table table,
  .users-table thead,
  .users-table tbody,
  .users-table th,
  .users-table td,
  .users-table tr {
    display: block;
  }
  
  .users-table thead tr {
    position: absolute;
    top: -9999px;
    left: -9999px;
  }
  
  .users-table tr {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 16px;
    padding: 16px;
  }
  
  .users-table td {
    border: none;
    position: relative;
    padding: 8px 0 8px 30%;
    text-align: left;
  }
  
  .users-table td:before {
    content: attr(data-label) ": ";
    position: absolute;
    left: 0;
    width: 25%;
    padding-right: 10px;
    white-space: nowrap;
    font-weight: 600;
    color: #374151;
  }
}
