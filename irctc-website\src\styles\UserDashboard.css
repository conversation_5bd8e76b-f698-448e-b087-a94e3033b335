.user-dashboard {
  min-height: calc(100vh - 140px); /* Adjust for header/footer */
  background-color: #f8fafc;
  padding: 20px 0;
}

.dashboard-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Credits Section */
.credits-section {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 30px;
  margin-bottom: 40px;
}

.credits-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px 30px;
  border-radius: 16px;
  text-align: center;
}

.credits-card h3 {
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  opacity: 0.9;
}

.credits-count {
  font-size: 48px;
  font-weight: 700;
  margin: 0 0 16px 0;
}

.credits-card p {
  margin: 0;
  opacity: 0.8;
  font-size: 14px;
}

.purchase-section {
  background: white;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.purchase-section h4 {
  margin: 0 0 24px 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
}

.purchase-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.purchase-card {
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px 16px;
  text-align: center;
  transition: all 0.2s ease;
  position: relative;
}

.purchase-card:hover {
  border-color: #3b82f6;
  transform: translateY(-2px);
}

.purchase-card.popular {
  border-color: #3b82f6;
  background-color: #f8fafc;
}

.popular-badge {
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  background: #3b82f6;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
}

.purchase-card h5 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 14px;
  font-weight: 600;
}

.purchase-card .price {
  color: #3b82f6;
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.purchase-card .price-per-credit {
  font-size: 12px;
  color: #718096;
  margin: 0 0 16px 0;
}

.purchase-btn {
  width: 100%;
  padding: 8px 16px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.purchase-btn:hover:not(:disabled) {
  background-color: #2563eb;
}

.purchase-btn:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

/* Guide Section */
.guide-section {
  background: white;
  padding: 30px;
  border-radius: 16px;
  margin-bottom: 40px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.guide-section h3 {
  margin: 0 0 30px 0;
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
}

.guide-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.step {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.step-number {
  width: 32px;
  height: 32px;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}

.step-content h4 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.step-content p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
}

/* Bookings Section */
.bookings-section {
  background: white;
  padding: 30px;
  border-radius: 16px;
  margin-bottom: 40px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.bookings-section h3 {
  margin: 0 0 24px 0;
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
}

.bookings-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.booking-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.booking-card:hover {
  border-color: #3b82f6;
  background-color: #f8fafc;
}

.booking-info h4 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.booking-info p {
  margin: 0 0 4px 0;
  color: #6b7280;
  font-size: 14px;
}

.booking-status {
  text-align: right;
}

.booking-status .status {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
}

.booking-status .status.confirmed {
  background-color: #dcfce7;
  color: #16a34a;
}

.booking-status .status.cancelled {
  background-color: #fecaca;
  color: #dc2626;
}

.booking-status p {
  margin: 8px 0 0 0;
  color: #9ca3af;
  font-size: 12px;
}

/* API Section */
.api-section {
  background: white;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.api-section h3 {
  margin: 0 0 20px 0;
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
}

.api-card {
  background: #f8fafc;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.api-card p {
  margin: 0 0 16px 0;
  color: #6b7280;
  font-size: 14px;
}

.api-key {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-bottom: 16px;
}

.api-key code {
  flex: 1;
  background: white;
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid #d1d5db;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  word-break: break-all;
}

.copy-btn {
  padding: 8px 16px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.copy-btn:hover {
  background-color: #2563eb;
}

.api-note {
  margin: 0;
  color: #f59e0b;
  font-size: 12px;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .credits-section {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .purchase-options {
    grid-template-columns: 1fr;
  }

  .guide-steps {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .booking-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .booking-status {
    text-align: left;
    width: 100%;
  }

  .api-key {
    flex-direction: column;
    align-items: stretch;
  }
}

@media (max-width: 480px) {
  .credits-card {
    padding: 30px 20px;
  }

  .credits-count {
    font-size: 36px;
  }

  .purchase-section,
  .guide-section,
  .bookings-section,
  .api-section {
    padding: 20px;
  }
}
