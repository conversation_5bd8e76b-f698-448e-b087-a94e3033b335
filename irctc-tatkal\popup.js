document.addEventListener('DOMContentLoaded', function() {
    // Get the auth buttons
    const googleAuthButton = document.getElementById('google-auth');
    const facebookAuthButton = document.getElementById('facebook-auth');
    const microsoftAuthButton = document.getElementById('microsoft-auth');
    const appleAuthButton = document.getElementById('apple-auth');
    
    // Disable other auth buttons for now
    microsoftAuthButton.disabled = true;
    appleAuthButton.disabled = true;

    // Update the loading state display
    function setButtonLoadingState(button, isLoading) {
        if (isLoading) {
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            button.disabled = true;
        } else {
            button.innerHTML = `<i class="fab fa-${button.id.split('-')[0]}"></i>`;
            button.disabled = false;
        }
    }

    // Add click event listener to Google auth button
    googleAuthButton.addEventListener('click', async () => {
        try {
            setButtonLoadingState(googleAuthButton, true);
            // Show loading state
            googleAuthButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Connecting...';

            // Get ID token using Chrome Identity API
            chrome.identity.getAuthToken({ 
                interactive: true,
                scopes: [
                    'https://www.googleapis.com/auth/userinfo.email',
                    'https://www.googleapis.com/auth/userinfo.profile',
                    'openid'
                ]
            }, async function(accessToken) {
                if (chrome.runtime.lastError) {
                    console.error('Chrome Identity Error:', chrome.runtime.lastError);
                    setButtonLoadingState(googleAuthButton, false);
                    alert('Authentication failed: ' + chrome.runtime.lastError.message);
                    return;
                }
                
                try {
                    // Get user info using the access token
                    const userInfoResponse = await fetch('https://www.googleapis.com/oauth2/v3/userinfo', {
                        headers: {
                            'Authorization': `Bearer ${accessToken}`
                        }
                    });

                    if (!userInfoResponse.ok) {
                        throw new Error('Failed to fetch user info');
                    }

                    const userInfo = await userInfoResponse.json();

                    // Get ID token
                    const tokenInfoResponse = await fetch(`https://oauth2.googleapis.com/tokeninfo?access_token=${accessToken}`);
                    const tokenInfo = await tokenInfoResponse.json();
                    
                    // Send to backend
                    const serverResponse = await fetch('http://localhost:3000/api/auth/google', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            token: accessToken,  // Send the access token
                            name: userInfo.name,
                            email: userInfo.email,
                            googleId: userInfo.sub,
                            picture: userInfo.picture
                        })
                    });

                    if (!serverResponse.ok) {
                        const errorData = await serverResponse.json();
                        throw new Error(errorData.error || 'Failed to authenticate with server');
                    }

                    const userData = await serverResponse.json();

                    // Store user info
                    await chrome.storage.local.set({
                        userInfo: {
                            name: userInfo.name,
                            email: userInfo.email,
                            picture: userInfo.picture,
                            id: userInfo.sub,
                            userId: userData.user.id,
                            apiKey: userData.user.apiKey
                        },
                        isLoggedIn: true,
                        authToken: userData.token
                    });

                    // Open dashboard and close popup
                    window.location.href = 'dashboard.html';

                } catch (error) {
                    console.error('Authentication error:', error);
                    setButtonLoadingState(googleAuthButton, false);
                    alert('Authentication failed: ' + error.message);
                }
            });

        } catch (error) {
            console.error('Authentication error:', error);
            setButtonLoadingState(googleAuthButton, false);
            alert('Authentication failed: ' + error.message);
        }
    });

    // Add click event listener to Facebook auth button
    facebookAuthButton.addEventListener('click', async () => {
        try {
            setButtonLoadingState(facebookAuthButton, true);
            // Show loading state
            facebookAuthButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Connecting...';

            console.log('Starting Facebook authentication...');
            
            // Wait for FB SDK to be loaded
            if (typeof FB === 'undefined') {
                console.log('Facebook SDK not loaded, skipping Facebook auth');
                alert('Facebook authentication is temporarily unavailable. Please use Google authentication.');
                setButtonLoadingState(facebookAuthButton, false);
                return;
            }

            await initializeFacebookSDK();
            console.log('Facebook SDK initialized successfully');

            // Trigger Facebook login
            const loginResponse = await new Promise((resolve, reject) => {
                FB.login((response) => {
                    console.log('FB.login response:', response);
                    if (response.authResponse) {
                        resolve(response);
                    } else {
                        reject(new Error('Facebook login cancelled or failed'));
                    }
                }, { 
                    scope: 'email,public_profile',
                    return_scopes: true
                });
            });

            console.log('Login successful, fetching user info...');

            // Get user info
            const userInfo = await new Promise((resolve, reject) => {
                FB.api('/me', { fields: 'name,email,picture' }, (response) => {
                    console.log('User info response:', response);
                    if (!response || response.error) {
                        reject(new Error('Failed to get user info from Facebook'));
                    } else {
                        resolve(response);
                    }
                });
            });

            if (!userInfo.email) {
                throw new Error('Email permission not granted');
            }

            console.log('Sending data to backend...');

            // Send to backend
            const serverResponse = await fetch(getApiUrl('/auth/facebook'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    accessToken: loginResponse.authResponse.accessToken,
                    name: userInfo.name,
                    email: userInfo.email,
                    facebookId: loginResponse.authResponse.userID,
                    picture: userInfo.picture?.data?.url
                })
            });

            if (!serverResponse.ok) {
                const errorData = await serverResponse.json();
                throw new Error(errorData.error || 'Failed to authenticate with server');
            }

            const userData = await serverResponse.json();
            console.log('Backend authentication successful');

            // Store user info
            await chrome.storage.local.set({
                userInfo: {
                    name: userInfo.name,
                    email: userInfo.email,
                    picture: userInfo.picture?.data?.url,
                    id: loginResponse.authResponse.userID,
                    userId: userData.user.id,
                    apiKey: userData.user.apiKey
                },
                isLoggedIn: true,
                authToken: userData.token
            });

            // Open dashboard and close popup
            window.location.href = 'dashboard.html';

        } catch (error) {
            console.error('Facebook authentication error:', error);
            setButtonLoadingState(facebookAuthButton, false);
            alert(`Authentication failed: ${error.message}`);
        }
    });

    // Check if user is already logged in
    chrome.storage.local.get(['isLoggedIn'], function(result) {
        if (result.isLoggedIn) {
            window.location.href = 'dashboard.html';
        }
    });

    // Add hover effects for better UX
    const authButtons = document.querySelectorAll('.auth-btn');
    authButtons.forEach(button => {
        if (!button.disabled) {
            button.addEventListener('mouseover', () => {
                button.style.transform = 'translateY(-2px)';
                button.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
            });

            button.addEventListener('mouseout', () => {
                button.style.transform = 'translateY(0)';
                button.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';
            });
        }
    });
});

// Initialize Facebook SDK
function initializeFacebookSDK() {
    return new Promise((resolve, reject) => {
        try {
            FB.init({
                appId: '1197354455232769', // Your Facebook App ID
                cookie: true,
                xfbml: true,
                version: 'v18.0'
            });
            
            // Check login status
            FB.getLoginStatus(function(response) {
                console.log('FB Login Status:', response);
                resolve();
            });
        } catch (error) {
            console.error('FB.init error:', error);
            reject(error);
        }
    });
}

// Note: This file contains authentication logic for the Chrome extension popup
// The minified code block has been removed to prevent conflicts
