/* Payment Modal Styles */
.payment-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.payment-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Header */
.payment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0 24px;
  border-bottom: 1px solid #e2e8f0;
  margin-bottom: 24px;
}

.payment-header h2 {
  color: #2d3748;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #718096;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #f7fafc;
  color: #4a5568;
}

/* Content */
.payment-content {
  padding: 0 24px 24px 24px;
}

/* Package Summary */
.package-summary {
  background: #f7fafc;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 24px;
  border: 1px solid #e2e8f0;
}

.package-summary h3 {
  color: #2d3748;
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
}

.package-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #4a5568;
}

.detail-row.total {
  padding-top: 12px;
  border-top: 1px solid #e2e8f0;
  font-weight: 600;
  font-size: 18px;
  color: #2d3748;
}

/* Payment Error */
.payment-error {
  background: #fed7d7;
  border: 1px solid #feb2b2;
  color: #c53030;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.payment-error p {
  margin: 0;
  font-size: 14px;
}

/* Payment Info */
.payment-info {
  margin-bottom: 24px;
}

.payment-info h4 {
  color: #2d3748;
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
}

.payment-info ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.payment-info li {
  color: #4a5568;
  padding: 6px 0;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Payment Actions */
.payment-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.cancel-btn,
.pay-btn {
  flex: 1;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.cancel-btn {
  background: #f7fafc;
  color: #4a5568;
  border: 1px solid #e2e8f0;
}

.cancel-btn:hover:not(:disabled) {
  background: #edf2f7;
  color: #2d3748;
}

.pay-btn {
  background: #667eea;
  color: white;
}

.pay-btn:hover:not(:disabled) {
  background: #5a67d8;
}

.pay-btn:disabled,
.cancel-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Payment Footer */
.payment-footer {
  background: #f7fafc;
  padding: 16px 24px;
  border-top: 1px solid #e2e8f0;
  border-radius: 0 0 12px 12px;
}

.payment-footer p {
  margin: 0;
  font-size: 12px;
  color: #718096;
  text-align: center;
  line-height: 1.4;
}

.payment-footer a {
  color: #667eea;
  text-decoration: none;
}

.payment-footer a:hover {
  text-decoration: underline;
}

/* Loading State */
.pay-btn:disabled {
  position: relative;
}

.pay-btn:disabled::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .payment-modal-overlay {
    padding: 10px;
  }

  .payment-modal {
    max-height: 95vh;
  }

  .payment-header {
    padding: 20px 20px 0 20px;
  }

  .payment-header h2 {
    font-size: 20px;
  }

  .payment-content {
    padding: 0 20px 20px 20px;
  }

  .package-summary {
    padding: 16px;
  }

  .payment-actions {
    flex-direction: column;
  }

  .payment-footer {
    padding: 12px 20px;
  }
}

@media (max-width: 480px) {
  .payment-modal-overlay {
    padding: 5px;
  }

  .payment-header {
    padding: 16px 16px 0 16px;
  }

  .payment-content {
    padding: 0 16px 16px 16px;
  }

  .package-summary {
    padding: 12px;
  }

  .detail-row {
    font-size: 14px;
  }

  .detail-row.total {
    font-size: 16px;
  }

  .payment-footer {
    padding: 10px 16px;
  }
}
