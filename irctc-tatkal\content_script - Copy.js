let user_data = {};
// Track automation status locally
let automationStatus = {
    state: 'running', // 'running' or 'paused'
    lastUpdated: Date.now()
};

function getMsg(e, t) {
    return {
        msg: {
            type: e,
            data: t
        },
        sender: "content_script",
        id: "irctc"
    }
}

function statusUpdate(e) {
    chrome.runtime.sendMessage(getMsg("status_update", {
        status: e,
        time: (new Date).toString().split(" ")[4]
    }))
}

// Helper function to check if automation should proceed
function shouldProceed() {
    // If stopped, never proceed
    if (automationStatus.state === 'stopped') {
        console.log('Automation is stopped. Not proceeding with action.');
        return false;
    }

    // If paused, don't proceed unless it's a status update
    if (automationStatus.state === 'paused') {
        console.log('Automation is paused. Not proceeding with action.');
        return false;
    }

    return true;
}

function classTranslator(e) {
    return labletext = "1A" === e ? "AC First Class (1A)" : "EV" === e ? "Vistadome AC (EV)" : "EC" === e ? "Exec. Chair Car (EC)" : "2A" === e ? "AC 2 Tier (2A)" : "3A" === e ? "AC 3 Tier (3A)" : "3E" === e ? "AC 3 Economy (3E)" : "CC" === e ? "AC Chair car (CC)" : "SL" === e ? "Sleeper (SL)" : "2S" === e ? "Second Sitting (2S)" : "None", labletext
}

function quotaTranslator(e) {
    return "GN" === e ? labletext = "GENERAL" : "TQ" === e ? labletext = "TATKAL" : "PT" === e ? labletext = "PREMIUM TATKAL" : "LD" === e ? labletext = "LADIES" : "SR" === e ? labletext = "LOWER BERTH/SR.CITIZEN" : labletext, labletext
}

function addDelay(e) {
    const t = Date.now();
    let o = null;
    do {
        o = Date.now()
    } while (o - t < e)
}
// Listen for automation status updates
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.action === 'updateAutomationStatus') {
        console.log('Received automation status update:', message.status);
        automationStatus = message.status;

        // If we're now running after being paused, log the status change
        if (automationStatus.state === 'running') {
            console.log('Automation resumed.');
        }

        sendResponse({ status: 'success' });
        return true;
    }
});

chrome.runtime.onMessage.addListener(((e, _, o) => {
    if ("irctc" !== e.id) return void o("Invalid Id");

    // Check if automation should proceed
    if (!shouldProceed() && e.msg.type !== "status_update") {
        console.log(`Skipping action ${e.msg.type} due to automation status: ${automationStatus.state}`);
        o(`Automation ${automationStatus.state}`);
        return true;
    }

    const r = e.msg.type;
    if ("selectJourney" === r) {
        console.log("selectJourney"), popupbtn = document.querySelectorAll(".btn.btn-primary"), popupbtn.length > 0 && (popupbtn[1].click(), console.log("Close last trxn popup"));
        const e = [...document.querySelector("#divMain > div > app-train-list").querySelectorAll(".tbis-div app-train-avl-enq")];
        console.log(user_data.journey_details["train-no"]);
        const t = user_data.journey_details["train-no"],
            o = e.filter((e => e.querySelector("div.train-heading").innerText.trim().includes(t.split("-")[0])))[0];
        if ("M" === user_data.travel_preferences.AvailabilityCheck) return void alert("Please manually select train and click Book");
        if ("A" === user_data.travel_preferences.AvailabilityCheck || "I" === user_data.travel_preferences.AvailabilityCheck) {
            if (!o) return console.log("Precheck - Train not found for search criteria."), void alert("Precheck - Train(" + t + ") not found for search criteria. You can manually proceed or correct data and restart the process.");
            const e = classTranslator(user_data.journey_details.class);
            if (![...o.querySelectorAll("table tr td div.pre-avl")].filter((t => t.querySelector("div").innerText === e))[0]) return console.log("Precheck - Selected Class not available in the train."), void alert("Precheck - Selected Class not available in the train. You can manually proceed or correct data and restart the process.")
        }
        const r = document.querySelector("div.row.col-sm-12.h_head1 > span > strong");
        if ("A" === user_data.travel_preferences.AvailabilityCheck)
            if (console.log("Automatically click"), "TQ" === user_data.journey_details.quota || "PT" === user_data.journey_details.quota || "GN" === user_data.journey_details.quota) {
                console.log("Verify tatkal time");
                const e = user_data.journey_details.class;
                requiredTime = "00:00:00", current_time = "00:00:00", ["1A", "2A", "3A", "CC", "EC", "3E"].includes(e.toUpperCase()) ? requiredTime = user_data.other_preferences.acbooktime : requiredTime = user_data.other_preferences.slbooktime, "GN" === user_data.journey_details.quota && (requiredTime = user_data.other_preferences.gnbooktime), console.log("requiredTime", requiredTime);
                var a = 0;
                let t = new MutationObserver((_ => {
                    if (current_time = (new Date).toString().split(" ")[4], console.log("current_time", current_time), current_time > requiredTime) t.disconnect(), selectJourney();
                    else {
                        if (0 == a) {
                            console.log("Inside wait counter 0 ");
                            try {
                                const e = document.createElement("div");
                                e.textContent = "Please wait..Booking will automatically start at " + requiredTime, e.style.textAlign = "center", e.style.color = "white", e.style.height = "auto", e.style.fontSize = "20px";
                                document.querySelector("#divMain > div > app-train-list > div> div > div > div.clearfix").insertAdjacentElement("afterend", e)
                            } catch (e) {
                                console.log("wait time failed", e.message)
                            }
                        }
                        try {
                            a % 2 == 0 ? (console.log("counter1", a % 2), document.querySelector("#divMain > div > app-train-list > div > div > div > div:nth-child(2)").style.background = "green") : (console.log("counter2", a % 2), document.querySelector("#divMain > div > app-train-list > div > div > div > div:nth-child(2)").style.background = "red")
                        } catch (e) { }
                        a += 1, console.log("wait time")
                    }
                }));
                t.observe(r, {
                    childList: !0,
                    subtree: !0,
                    characterDataOldValue: !0
                })
            } else console.log("select journey GENERAL quota"), selectJourney();
        else "I" === user_data.travel_preferences.AvailabilityCheck && (console.log("Immediately click"), selectJourney())
    } else if ("fillPassengerDetails" === r) {
        console.log("fillPassengerDetails message received");
        fillPassengerDetails();
    }
    else if ("reviewBooking" === r) {
        console.log("reviewBooking message received");

        // Function to check if the page is ready for captcha filling
        function checkPageReadyAndFillCaptcha() {
            console.log("Checking if review booking page is ready for captcha filling");

            // Check if loader is visible
            const loader = document.querySelector("#loaderP");
            if (loader && window.getComputedStyle(loader).display !== "none") {
                console.log("Loader is still visible on review page. Waiting...");
                setTimeout(checkPageReadyAndFillCaptcha, 1000);
                return;
            }

            // Try multiple selectors for the captcha input field
            const captchaInputSelectors = [
                "#captcha",
                "input[name='captcha']",
                "input[formcontrolname='captcha']",
                "#divMain input[type='text'][autocomplete='off']",
                "input.captchaTextBox"
            ];

            let captchaInput = null;

            // Try each selector until we find a match
            for (const selector of captchaInputSelectors) {
                const input = document.querySelector(selector);
                if (input) {
                    captchaInput = input;
                    console.log(`Found captcha input with selector: ${selector}`);
                    break;
                }
            }

            if (!captchaInput) {
                console.log("Captcha input not found yet. Waiting...");
                setTimeout(checkPageReadyAndFillCaptcha, 1000);
                return;
            }

            console.log("Review booking page is ready for captcha filling");

            // Scroll to captcha field
            try {
                captchaInput.scrollIntoView({
                    behavior: "smooth",
                    block: "center",
                    inline: "nearest"
                });
                console.log("Scrolled to captcha field");
            } catch (error) {
                console.error("Error scrolling to captcha field:", error);
            }

            // Handle captcha
            if (user_data.other_preferences && user_data.other_preferences.autoCaptcha) {
                console.log("Auto captcha is enabled, calling getCaptcha()");
                // Use the Ninjas API for captcha
                setTimeout(() => {
                    getCaptcha();
                }, 500);
            } else {
                console.log("Manual captcha filling");
                let placeholderText = "X";
                captchaInput.value = placeholderText;
                captchaInput.dispatchEvent(new Event("input"));
                captchaInput.dispatchEvent(new Event("change"));
                captchaInput.focus();
            }
        }

        // Check if we have user_data
        if (!user_data || Object.keys(user_data).length === 0) {
            console.log("User data not available in reviewBooking, loading from storage...");

            // Load user data from storage and then continue
            chrome.storage.local.get(null, function(data) {
                console.log("User data loaded from storage in reviewBooking");
                user_data = data;

                // Start checking if the page is ready
                checkPageReadyAndFillCaptcha();
            });
        } else {
            // We already have user data, proceed
            checkPageReadyAndFillCaptcha();
        }
    } else if ("bkgPaymentOptions" === r) {
        addDelay(200), console.log("bkgPaymentOptions");
        let e = "Multiple Payment Service",
            t = "IRCTC iPay (Credit Card/Debit Card/UPI)",
            o = !0;
        if (user_data.other_preferences.paymentmethod.includes("IRCUPI") && (o = !1, e = "IRCTC iPay (Credit Card/Debit Card/UPI)", t = "Credit cards/Debit cards/Netbanking/UPI (Powered by IRCTC)", console.log("Payment option-IRCUPI")), user_data.other_preferences.paymentmethod.includes("PAYTMUPI") && (e = "BHIM/ UPI/ USSD", t = "Pay using BHIM (Powered by PAYTM ) also accepts UPI", console.log("Payment option-PAYTMUPI")), user_data.other_preferences.paymentmethod.includes("PHONEPEUPI") && (e = "Multiple Payment Service", t = "Credit & Debit cards / Wallet / UPI (Powered by PhonePe)", console.log("Payment option-PHONEPEUPI")), user_data.other_preferences.paymentmethod.includes("MOBUPI")) {
            const o = window.navigator.userAgent;
            console.log("BrowserUserAgent", o), o.includes("Android") && (console.log("Android browser"), e = "Multiple Payment Service", t = "Credit & Debit cards / Wallet / UPI (Powered by PhonePe)")
        }
        user_data.other_preferences.paymentmethod.includes("IRCWA") && (e = "IRCTC eWallet", t = "IRCTC eWallet", console.log("Payment option-IRCWA")), user_data.other_preferences.paymentmethod.includes("HDFCDB") && (e = "Payment Gateway / Credit Card / Debit Card", t = "Visa/Master Card(Powered By HDFC BANK)", console.log("Payment option-HDFCDB"));
        let r = t.replace("&", "&amp;"),
            a = !1;
        var n = setInterval((() => {
            if (document.getElementsByClassName("bank-type").length > 1) {
                clearInterval(n);
                var t = document.getElementById("pay-type").getElementsByTagName("div");
                for (i = 0; i < t.length; i++) t[i].innerText.indexOf(e) >= 0 && (o && t[i].click(), setTimeout((() => {
                    var e = document.getElementsByClassName("border-all no-pad");
                    for (i = 0; i < e.length; i++) {
                        if (0 != e[i].getBoundingClientRect().top && -1 != e[i].getElementsByTagName("span")[0].innerHTML.toUpperCase().indexOf(r.toUpperCase())) {
                            o && e[i].click(), a = !0, document.getElementsByClassName("btn-primary")[0].scrollIntoView({
                                behavior: "smooth",
                                block: "center",
                                inline: "nearest"
                            }), user_data.other_preferences.hasOwnProperty("paymentManual") && user_data.other_preferences.paymentManual ? alert("Manually submit the payment page.") : setTimeout((() => {
                                document.getElementsByClassName("btn-primary")[0].click()
                            }), 500);
                            break
                        }
                        i != e.length - 1 || a || alert("Selected payment option not available, please select other option manually.")
                    }
                }), 500))
            }
        }), 500)
    } else console.log("Nothing to do");
    o("Something went wrong")
}));
let captchaRetry = 0;

// Helper function to convert base64 to File object
function base64ToFile(base64, filename) {
    // Add data:image/jpeg;base64, prefix if it's not there
    if (!base64.startsWith('data:')) {
        base64 = 'data:image/jpeg;base64,' + base64;
    }

    // Split the base64 string to get mime type and actual data
    const arr = base64.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const bstr = atob(arr[1]); // Decode Base64
    let n = bstr.length;
    const u8arr = new Uint8Array(n);

    while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
    }

    return new File([u8arr], filename, { type: mime });
}

function getCaptcha() {
    if (captchaRetry < 100) {
        console.log("getCaptcha - Using Ninjas API"), captchaRetry += 1;

        // Try multiple selectors for the captcha image
        const captchaSelectors = [
            ".captcha-img",                                // Standard selector
            "#nlpImgContainer img",                        // Alternative selector
            "img[src*='captcha']",                         // Any image with 'captcha' in src
            "div.captchaImg img",                          // Another possible selector
            "#divMain img[src*='nlpCaptcha']",             // IRCTC review page specific
            "#divMain div.col-sm-12 img"                   // Generic div containing image
        ];

        let captchaImg = null;

        // Try each selector until we find a match
        for (const selector of captchaSelectors) {
            const img = document.querySelector(selector);
            if (img) {
                captchaImg = img;
                console.log(`Found captcha image with selector: ${selector}`);
                break;
            }
        }

        if (captchaImg) {
            try {
                // Log the captcha image details to help with debugging
                console.log("Captcha image found:", captchaImg);
                console.log("Captcha image src:", captchaImg.src);
                console.log("Captcha image dimensions:", captchaImg.width, "x", captchaImg.height);

                // Get the base64 image data from the src attribute
                const imageData = captchaImg.src;
                console.log("Got captcha image data");

                // Check if the image data is valid
                if (!imageData || imageData === "" || !imageData.includes("data:") && !imageData.includes("http")) {
                    console.error("Invalid captcha image data");
                    setTimeout(() => {
                        getCaptcha();
                    }, 2000);
                    return;
                }

                // Convert base64 to File object
                const captchaFile = base64ToFile(imageData, 'captcha.jpg');
                console.log("Converted base64 to File object");

                // Create FormData and append the file
                const formData = new FormData();
                formData.append('image', captchaFile);
                console.log("Created FormData with image file");

                // Use XMLHttpRequest instead of jQuery
                const xhr = new XMLHttpRequest();
                xhr.open('POST', 'https://api.api-ninjas.com/v1/imagetotext', true);
                xhr.setRequestHeader('X-Api-Key', 'TjdUJmr85s2ODK5ozzw62w==jgX8T7CZ2j9F5wDp');

                xhr.onload = function() {
                    if (xhr.status === 200) {
                        const response = JSON.parse(xhr.responseText);
                        console.log("Ninjas API response:", response);

                        try {
                            // Extract text from the response
                            let captchaText = "";
                            if (response && response.length > 0) {
                                // Combine all text from the response
                                for (const item of response) {
                                    captchaText += item.text;
                                }
                            }

                            console.log("Extracted captcha text:", captchaText);

                            // Clean up the captcha text (remove spaces and special characters)
                            const validChars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789=@";

                            // Apply specific replacements for common OCR errors
                            captchaText = captchaText.replace(/\s+/g, "")  // Remove all whitespace
                                                   .replace(/[oO0]/g, "0")  // Convert o/O to 0
                                                   .replace(/[iIl|]/g, "I") // Convert i/l/| to I
                                                   .replace(/[sS5]/g, "S")  // Convert 5 to S
                                                   .replace(/[zZ2]/g, "Z")  // Convert 2 to Z
                                                   .replace(/[gG6]/g, "G")  // Convert 6 to G
                                                   .replace(/[bB8]/g, "B")  // Convert 8 to B
                                                   .replace(/[)}\]]/, "J")  // Convert )/}/] to J
                                                   .replace(/[({[]/, "I");  // Convert (/{ to I

                            const captchaChars = Array.from(captchaText);

                            let cleanedCaptchaText = "";
                            for (const char of captchaChars) {
                                if (validChars.includes(char)) {
                                    cleanedCaptchaText += char;
                                }
                            }

                            console.log("Cleaned captcha text:", cleanedCaptchaText);

                            // Try multiple selectors for the captcha input field
                            const captchaInputSelectors = [
                                "#captcha",
                                "input[name='captcha']",
                                "input[formcontrolname='captcha']",
                                "#divMain input[type='text'][autocomplete='off']",
                                "input.captchaTextBox"
                            ];

                            let captchaInput = null;

                            // Try each selector until we find a match
                            for (const selector of captchaInputSelectors) {
                                const input = document.querySelector(selector);
                                if (input) {
                                    captchaInput = input;
                                    console.log(`Found captcha input with selector: ${selector}`);
                                    break;
                                }
                            }

                            if (!captchaInput) {
                                console.error("Captcha input field not found");
                                setTimeout(() => {
                                    getCaptcha();
                                }, 2000);
                                return;
                            }

                            // Fill the captcha input field
                            captchaInput.value = cleanedCaptchaText;
                            console.log("Set captcha input value to:", cleanedCaptchaText);

                            // If no text was extracted, refresh the captcha and try again
                            if (cleanedCaptchaText === "") {
                                console.log("Null captcha text from API");

                                // Try multiple selectors for the refresh button
                                const refreshButtonSelectors = [
                                    ".glyphicon.glyphicon-repeat",
                                    "a.refreshIcon",
                                    "img[src*='refresh']",
                                    "button.captchaRefresh",
                                    "a[title='Refresh']"
                                ];

                                let refreshButton = null;

                                // Try each selector until we find a match
                                for (const selector of refreshButtonSelectors) {
                                    const button = document.querySelector(selector);
                                    if (button) {
                                        refreshButton = button;
                                        console.log(`Found refresh button with selector: ${selector}`);
                                        break;
                                    }
                                }

                                if (refreshButton) {
                                    if (refreshButton.click) {
                                        refreshButton.click();
                                    } else if (refreshButton.parentElement && refreshButton.parentElement.click) {
                                        refreshButton.parentElement.click();
                                    }

                                    setTimeout(() => {
                                        getCaptcha();
                                    }, 2000);
                                    return;
                                }
                            }

                            // Trigger input events to notify the form
                            captchaInput.dispatchEvent(new Event("input"));
                            captchaInput.dispatchEvent(new Event("change"));
                            captchaInput.focus();

                            console.log("Dispatched input events and focused captcha field");

                            // Set up observers to detect invalid captcha messages
                            const loginElement = document.querySelector("app-login");
                            const reviewElement = document.querySelector("#divMain > div > app-review-booking");
                            const toastElements = document.querySelectorAll("p-toast");

                            console.log("Setting up observers for captcha validation messages");

                            let observer = new MutationObserver((mutations) => {
                                // Check for error messages in the mutations
                                for (const mutation of mutations) {
                                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                                        for (const node of mutation.addedNodes) {
                                            if (node.nodeType === Node.ELEMENT_NODE || node.nodeType === Node.TEXT_NODE) {
                                                const text = node.textContent || node.innerText || '';
                                                if (text.toLowerCase().includes("valid captcha") ||
                                                    text.toLowerCase().includes("captcha invalid") ||
                                                    text.toLowerCase().includes("incorrect captcha")) {

                                                    console.log("Invalid captcha detected:", text);

                                                    // Refresh and try again
                                                    setTimeout(() => {
                                                        getCaptcha();
                                                    }, 2000);

                                                    return;
                                                }
                                            }
                                        }
                                    }
                                }
                            });

                            // Observe login element for changes
                            if (loginElement) {
                                console.log("Observing login element for captcha validation messages");
                                observer.observe(loginElement, {
                                    childList: true,
                                    subtree: true,
                                    characterData: true
                                });
                            }

                            // Observe review element for changes
                            if (reviewElement) {
                                console.log("Observing review element for captcha validation messages");
                                observer.observe(reviewElement, {
                                    childList: true,
                                    subtree: true,
                                    characterData: true
                                });
                            }

                            // Observe toast elements for changes
                            toastElements.forEach(element => {
                                console.log("Observing toast element for captcha validation messages");
                                observer.observe(element, {
                                    childList: true,
                                    subtree: true,
                                    characterData: true
                                });
                            });

                            // Auto-submit if configured
                            if (user_data.other_preferences &&
                                user_data.other_preferences.CaptchaSubmitMode === "A") {
                                console.log("Auto-submit is enabled, calling handleAutoSubmit()");
                                handleAutoSubmit();
                            }
                        } catch (error) {
                            console.error("Error processing Ninjas API response:", error);
                            // Retry on error
                            setTimeout(() => {
                                getCaptcha();
                            }, 2000);
                        }
                    } else {
                        console.error(`Error ${xhr.status}: ${xhr.statusText}`);
                        console.error("Response:", xhr.responseText);
                        // Retry on error
                        setTimeout(() => {
                            getCaptcha();
                        }, 2000);
                    }
                };

                xhr.onerror = function() {
                    console.error("Ninjas API Request failed");
                    // Retry on error
                    setTimeout(() => {
                        getCaptcha();
                    }, 2000);
                };

                // Send the request with the form data
                xhr.send(formData);
                console.log("Sent request to Ninjas API");

            } catch (error) {
                console.error("Error preparing captcha image for API:", error);
                setTimeout(() => {
                    getCaptcha();
                }, 2000);
            }

        } else {
            console.log("Captcha image not found, waiting for it to load");
            setTimeout(() => {
                getCaptcha();
            }, 2000);
        }
    } else {
        console.log("Maximum captcha retry attempts reached");
    }
}

// Helper function to handle auto-submission of forms after captcha is filled
function handleAutoSubmit() {
    console.log("Auto submit captcha");

    // Check if we're on the login page
    const loginPage = document.querySelector("#divMain > app-login");
    if (loginPage) {
        console.log("Detected login page, preparing to submit login form");

        // Try multiple selectors for the submit button
        const submitButtonSelectors = [
            "button[type='submit'][class='search_btn train_Search']",
            "button[type='submit'][class='search_btn train_Search train_Search_custom_hover']",
            "button.search_btn.train_Search",
            "button[type='submit']"
        ];

        let submitButton = null;

        // Try each selector until we find a match
        for (const selector of submitButtonSelectors) {
            const button = loginPage.querySelector(selector);
            if (button) {
                submitButton = button;
                console.log(`Found login submit button with selector: ${selector}`);
                break;
            }
        }

        const usernameInput = loginPage.querySelector("input[type='text'][formcontrolname='userid']");
        const passwordInput = loginPage.querySelector("input[type='password'][formcontrolname='password']");

        if (usernameInput && passwordInput && usernameInput.value !== "" && passwordInput.value !== "") {
            console.log("Username and password are filled, submitting login form");
            setTimeout(() => {
                try {
                    if (submitButton) {
                        console.log("Clicking login submit button");
                        submitButton.click();
                    } else {
                        console.error("Login submit button not found");
                        alert("Login submit button not found, please submit manually");
                    }
                } catch (e) {
                    console.error("Error clicking login submit button:", e);
                    alert("Error submitting login form, please submit manually");
                }
            }, 1000);
        } else {
            console.error("Username or password not filled");
            alert("Unable to auto submit login info, username and password not filled, please submit manually");
        }
    }

    // Check if we're on the review page
    const reviewPage = document.querySelector("#divMain > div > app-review-booking");
    if (reviewPage) {
        console.log("Detected review booking page, preparing to submit");

        // Try multiple selectors for the captcha input
        const captchaInputSelectors = [
            "#captcha",
            "input[name='captcha']",
            "input[formcontrolname='captcha']",
            "#divMain input[type='text'][autocomplete='off']",
            "input.captchaTextBox"
        ];

        let captchaInput = null;

        // Try each selector until we find a match
        for (const selector of captchaInputSelectors) {
            const input = document.querySelector(selector);
            if (input) {
                captchaInput = input;
                console.log(`Found captcha input with selector: ${selector}`);
                break;
            }
        }

        if (!captchaInput) {
            console.error("Captcha input not found");
            alert("Captcha input not found, please submit manually");
            return;
        }

        if (captchaInput.value !== "") {
            console.log("Captcha is filled with:", captchaInput.value);

            // Try multiple selectors for the submit button
            const submitButtonSelectors = [
                ".btnDefault.train_Search",
                "button.train_Search",
                "button[type='submit']",
                "input[type='submit']",
                "button.btnDefault",
                "#divMain button.train_Search"
            ];

            let submitButton = null;

            // Try each selector until we find a match
            for (const selector of submitButtonSelectors) {
                const button = document.querySelector(selector);
                if (button) {
                    submitButton = button;
                    console.log(`Found review submit button with selector: ${selector}`);
                    break;
                }
            }

            if (submitButton) {
                console.log("Found submit button, preparing to click it");

                setTimeout(() => {
                    try {
                        if (user_data.other_preferences && user_data.other_preferences.confirmberths) {
                            console.log("Confirm berths option is enabled");

                            // Try multiple selectors for availability indicators
                            const availabilitySelectors = [
                                ".AVAILABLE",
                                ".avl-color",
                                "span.avl",
                                "td.avl",
                                "div.avl"
                            ];

                            let availabilityElement = null;

                            // Try each selector until we find a match
                            for (const selector of availabilitySelectors) {
                                const element = document.querySelector(selector);
                                if (element) {
                                    availabilityElement = element;
                                    console.log(`Found availability element with selector: ${selector}`);
                                    break;
                                }
                            }

                            if (availabilityElement) {
                                console.log("Seats are available, proceeding with booking");
                                console.log("Clicking submit button");
                                submitButton.click();
                            } else {
                                console.log("No seats available, asking for confirmation");
                                if (confirm("No seats Available, Do you still want to continue booking?") !== true) {
                                    console.log("User chose to stop booking due to no seats");
                                    return;
                                }
                                console.log("User chose to proceed despite no seats");
                                console.log("Clicking submit button");
                                submitButton.click();
                            }
                        } else {
                            console.log("Confirm berths option is not enabled, proceeding with booking");
                            console.log("Clicking submit button");
                            submitButton.click();
                        }
                    } catch (e) {
                        console.error("Error during review submission:", e);
                        alert("Error submitting review form, please submit manually");
                    }
                }, 1000);
            } else {
                console.error("Review submit button not found");
                alert("Review submit button not found, please submit manually");
            }
        } else {
            console.error("Captcha is not filled");
            alert("Captcha is not filled, please fill and submit manually");
        }
    }

    // Check if we're on any other page with a captcha
    if (!loginPage && !reviewPage) {
        console.log("Not on a recognized page with captcha, looking for generic submit buttons");

        // Try to find any submit button on the page
        const submitButtonSelectors = [
            "button[type='submit']",
            "input[type='submit']",
            "button.btnDefault",
            "button.train_Search",
            "button.search_btn"
        ];

        let submitButton = null;

        // Try each selector until we find a match
        for (const selector of submitButtonSelectors) {
            const button = document.querySelector(selector);
            if (button) {
                submitButton = button;
                console.log(`Found generic submit button with selector: ${selector}`);
                break;
            }
        }

        if (submitButton) {
            console.log("Found generic submit button, clicking it");
            setTimeout(() => {
                try {
                    submitButton.click();
                } catch (e) {
                    console.error("Error clicking generic submit button:", e);
                }
            }, 1000);
        } else {
            console.log("No generic submit button found");
        }
    }
}

function loadLoginDetails() {
    console.log("loadLoginDetails");

    // Function to check if the login form is ready and fill it
    function checkLoginFormAndFill() {
        const loginForm = document.querySelector("#divMain > app-login");
        if (!loginForm) {
            console.log("Login form not found yet, retrying in 1 second...");
            setTimeout(checkLoginFormAndFill, 1000);
            return;
        }

        console.log("Login form found");

        // Fill username and password
        const usernameInput = loginForm.querySelector("input[type='text'][formcontrolname='userid']");
        const passwordInput = loginForm.querySelector("input[type='password'][formcontrolname='password']");

        if (!usernameInput || !passwordInput) {
            console.log("Username or password fields not found yet, retrying in 1 second...");
            setTimeout(checkLoginFormAndFill, 1000);
            return;
        }

        // Fill username
        usernameInput.value = user_data.irctc_credentials.user_name ?? "";
        usernameInput.dispatchEvent(new Event("input"));
        usernameInput.dispatchEvent(new Event("change"));

        // Fill password
        passwordInput.value = user_data.irctc_credentials.password ?? "";
        passwordInput.dispatchEvent(new Event("input"));
        passwordInput.dispatchEvent(new Event("change"));

        console.log("Username and password filled");

        // Handle captcha
        const captchaInput = loginForm.querySelector("#captcha");
        if (!captchaInput) {
            console.log("Captcha field not found yet, retrying in 1 second...");
            setTimeout(checkLoginFormAndFill, 1000);
            return;
        }

        console.log("Captcha field found");

        // Scroll to captcha
        captchaInput.scrollIntoView({
            behavior: "smooth",
            block: "center",
            inline: "nearest"
        });

        // Set up a mutation observer to detect invalid captcha messages
        const loginObserver = new MutationObserver((mutations) => {
            for (const mutation of mutations) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    for (const node of mutation.addedNodes) {
                        if (node.nodeType === Node.ELEMENT_NODE || node.nodeType === Node.TEXT_NODE) {
                            const text = node.textContent || node.innerText || '';
                            if (text.toLowerCase().includes("valid captcha") ||
                                text.toLowerCase().includes("captcha invalid") ||
                                text.toLowerCase().includes("incorrect captcha")) {

                                console.log("Invalid captcha detected on login page:", text);

                                // Refresh and try again
                                if (user_data.other_preferences.autoCaptcha) {
                                    console.log("Retrying captcha on login page");
                                    setTimeout(() => {
                                        // Reset captchaRetry counter to ensure we don't hit the limit
                                        captchaRetry = 0;
                                        getCaptcha();
                                    }, 1000);
                                }

                                return;
                            }
                        }
                    }
                }
            }
        });

        // Start observing the login form for invalid captcha messages
        loginObserver.observe(loginForm, {
            childList: true,
            subtree: true,
            characterData: true
        });

        // Handle captcha filling
        if (user_data.other_preferences.autoCaptcha) {
            console.log("Auto captcha is enabled for login");

            // Set up a retry mechanism for login captcha
            let loginCaptchaRetryCount = 0;
            const maxLoginCaptchaRetries = 5;

            function tryLoginCaptcha() {
                loginCaptchaRetryCount++;
                console.log(`Attempting to solve login captcha (attempt ${loginCaptchaRetryCount}/${maxLoginCaptchaRetries})`);

                // Reset captchaRetry counter to ensure we don't hit the limit in getCaptcha
                captchaRetry = 0;

                // Call getCaptcha to solve the captcha
                getCaptcha();

                // Set up a check to see if the captcha was successfully filled
                setTimeout(() => {
                    const captchaValue = captchaInput.value;
                    console.log(`Current captcha value: ${captchaValue}`);

                    // If captcha is empty or just the placeholder, try again
                    if (!captchaValue || captchaValue === "X" || captchaValue.length < 3) {
                        if (loginCaptchaRetryCount < maxLoginCaptchaRetries) {
                            console.log("Captcha not properly filled, retrying...");

                            // Try to refresh the captcha if possible
                            try {
                                const refreshButton = document.querySelector(".glyphicon.glyphicon-repeat");
                                if (refreshButton && refreshButton.parentElement) {
                                    refreshButton.parentElement.click();
                                    console.log("Clicked captcha refresh button");
                                }
                            } catch (error) {
                                console.error("Error refreshing captcha:", error);
                            }

                            // Wait a moment and try again
                            setTimeout(tryLoginCaptcha, 2000);
                        } else {
                            console.log("Max login captcha retries reached");
                        }
                    } else {
                        console.log("Login captcha appears to be filled successfully");
                    }
                }, 3000);
            }

            // Start the login captcha solving process
            setTimeout(tryLoginCaptcha, 500);
        } else {
        console.log("Manual captcha filling");
        let placeholderText = "X";
        const captchaInput = document.querySelector("#captcha");
        captchaInput.value = placeholderText;
        captchaInput.dispatchEvent(new Event("input"));
        captchaInput.dispatchEvent(new Event("change"));
        captchaInput.focus();
    }

    // Start checking for the login form
    checkLoginFormAndFill();
}

function loadJourneyDetails() {
    console.log("filling_journey_details");
    const e = document.querySelector("app-jp-input form"),
        t = e.querySelector("#origin > span > input");
    t.value = user_data.journey_details.from, t.dispatchEvent(new Event("keydown")), t.dispatchEvent(new Event("input"));
    const o = e.querySelector("#destination > span > input");
    o.value = user_data.journey_details.destination, o.dispatchEvent(new Event("keydown")), o.dispatchEvent(new Event("input"));
    const r = e.querySelector("#jDate > span > input");
    r.value = user_data.journey_details.date ? `${user_data.journey_details.date.split("-").reverse().join("/")}` : "", r.dispatchEvent(new Event("keydown")), r.dispatchEvent(new Event("input"));
    const a = e.querySelector("#journeyClass");
    a.querySelector("div > div[role='button']").click(), addDelay(300), [...a.querySelectorAll("ul li")].filter((e => e.innerText === classTranslator(user_data.journey_details.class) ?? ""))[0]?.click(), addDelay(300);
    const n = e.querySelector("#journeyQuota");
    n.querySelector("div > div[role='button']").click(), [...n.querySelectorAll("ul li")].filter((e => e.innerText === quotaTranslator(user_data.journey_details.quota) ?? ""))[0]?.click(), addDelay(300);
    const l = e.querySelector("button.search_btn.train_Search[type='submit']");
    addDelay(300), console.log("filled_journey_details"), l.click()
}

// Old selectJourney function removed - replaced by newer implementation

function retrySelectJourney() {
    console.log("Retrying selectJourney...");
    setTimeout(() => {
        // Make sure we have user data before retrying
        if (!user_data || Object.keys(user_data).length === 0) {
            chrome.storage.local.get(null, function(data) {
                user_data = data;
                selectJourneyWithData();
            });
        } else {
            selectJourneyWithData();
        }
    }, 1000);
}

function selectJourney() {
    console.log("selectJourney function started executing");
    console.log("Current URL:", window.location.href);

    // Check if automation should proceed
    if (!shouldProceed()) {
        console.log("Automation is " + automationStatus.state + ". Not proceeding with selectJourney.");
        return;
    }

    // First, ensure we have user_data
    if (!user_data || Object.keys(user_data).length === 0) {
        console.log("User data not available, loading from storage...");

        // Load user data from storage and then continue
        chrome.storage.local.get(null, function(data) {
            console.log("User data loaded from storage in selectJourney");
            user_data = data;

            // Call the function again now that we have the data
            setTimeout(() => {
                selectJourneyWithData();
            }, 500);
        });
        return;
    } else {
        // We already have user data, proceed
        selectJourneyWithData();
    }
}

function selectJourneyWithData() {
    console.log("selectJourneyWithData function executing with user data");
    console.log("User data available:", !!user_data);
    console.log("Journey details available:", !!user_data?.journey_details);

    // Check if we're on the train list page
    if (!window.location.href.includes("nget/booking/train-list")) {
        console.error("Not on train list page. Current URL:", window.location.href);
        return;
    }

    const e = setInterval((() => {
        const t = document.querySelector("#divMain > div > app-train-list > p-toast > div > p-toastitem > div > div > a"),
            o = document.querySelector("body > app-root > app-home > div.header-fix > app-header > p-toast > div > p-toastitem > div > div > a"),
            r = t || o,
            a = document.querySelector("#loaderP"),
            n = a && "none" !== a.style.display;
        r && !n && (console.log("Toast link found. Clicking it now..."), r.click(), console.log("Toast link clicked"), retrySelectJourney(), console.log("Toast link clicked and called retrySelectJourney"), clearInterval(e))
    }), 1e3);

    if (!user_data?.journey_details?.["train-no"]) {
        console.error("Train number is not available in user_data.");
        console.log("Available journey details:", JSON.stringify(user_data?.journey_details));
        return;
    }

    const t = document.querySelector("#divMain > div > app-train-list");
    if (!t) {
        console.error("Train list parent not found.");
        return;
    }

    console.log("Train list parent found, searching for trains...");
    const o = Array.from(t.querySelectorAll(".tbis-div app-train-avl-enq"));
    console.log("Found " + o.length + " trains in the list");

    const r = user_data.journey_details["train-no"],
        a = classTranslator(user_data.journey_details.class),
        n = new Date(user_data.journey_details.date),
        l = n.toDateString().split(" ")[0] + ", " + n.toDateString().split(" ")[2] + " " + n.toDateString().split(" ")[1];
    console.log("Train Number:", r), console.log("Class:", a), console.log("date", l);

    const i = o.find((e => e.querySelector("div.train-heading").innerText.trim().includes(r.split("-")[0])));
    if (!i) {
        console.error("Train not found in the list.");
        console.log("Available trains:", o.map(train => train.querySelector("div.train-heading").innerText.trim()));
        statusUpdate("journey_selection_stopped.no_train");
        return;
    }

    console.log("Train found:", i.querySelector("div.train-heading").innerText.trim());
    const c = e => {
        if (!e) return !1;
        const t = window.getComputedStyle(e);
        return "none" !== t.display && "hidden" !== t.visibility && "0" !== t.opacity
    },
        s = Array.from(i.querySelectorAll("table tr td div.pre-avl")).find((e => e.querySelector("div").innerText.trim() === a)),
        d = Array.from(i.querySelectorAll("span")).find((e => e.innerText.trim() === a)),
        u = s || d;
    if (console.log("FOUND updatedClassToClick:", u), !u) return void console.error("Class to click not found.");
    const p = document.querySelector("#loaderP");
    if (c(p)) return void console.error("Loader is visible. Cannot click the class.");
    let h;
    u.click();
    new MutationObserver(((_, t) => {
        console.log("Mutation observed at", (new Date).toLocaleTimeString()), clearTimeout(h), h = setTimeout((() => {
            const e = Array.from(i.querySelectorAll("div div table td div.pre-avl")).find((e => e.querySelector("div").innerText.trim() === l));
            console.log("FOUND classTabToSelect:", e), e ? (e.click(), console.log("Clicked on selectdate"), setTimeout((() => {
                const e = () => {
                    const o = i.querySelector("button.btnDefault.train_Search.ng-star-inserted");
                    if (c(document.querySelector("#loaderP"))) return console.warn("Loader is visible, retrying..."), void setTimeout(e, 100);
                    !o || o.classList.contains("disable-book") || o.disabled ? (console.warn("bookBtn is disabled or not found, retrying..."), retrySelectJourney()) : setTimeout((() => {
                        o.click(), console.log("Clicked on bookBtn"), clearTimeout(h), t.disconnect()
                    }), 300)
                };
                e()
            }), 1e3)) : console.warn("classTabToSelect not found")
        }), 300)
    })).observe(i, {
        attributes: !1,
        childList: !0,
        subtree: !0
    })
}
let keyCounter = 0;

function fillPassengerDetails() {
    console.log("passenger_filling_started");

    // First, ensure we have user_data
    if (!user_data || Object.keys(user_data).length === 0) {
        console.log("User data not available in fillPassengerDetails, loading from storage...");

        // Load user data from storage and then continue
        chrome.storage.local.get(null, function(data) {
            console.log("User data loaded from storage in fillPassengerDetails");
            user_data = data;

            // Call the function again now that we have the data
            setTimeout(() => {
                fillPassengerDetailsWithData();
            }, 500);
        });
        return;
    } else {
        // We already have user data, proceed
        fillPassengerDetailsWithData();
    }
}

function fillPassengerDetailsWithData() {
    console.log("fillPassengerDetailsWithData executing with user data");
    console.log("User data available:", !!user_data);
    console.log("Passenger details available:", !!user_data?.passenger_details);

    try {
        // Check if we're on the passenger input page
        if (!window.location.href.includes("nget/booking/psgninput")) {
            console.error("Not on passenger input page. Current URL:", window.location.href);
            return;
        }

        // Check if loader is visible and wait for it to disappear
        const loader = document.querySelector("#loaderP");
        if (loader && window.getComputedStyle(loader).display !== "none") {
            console.log("Loader is visible. Waiting for it to disappear before filling passenger details...");
            setTimeout(fillPassengerDetailsWithData, 1000);
            return;
        }

        // Wait for the passenger input form to be fully loaded
        const passengerInputElement = document.querySelector("app-passenger-input");
        if (!passengerInputElement) {
            console.error("Passenger input form not found. Retrying in 1 second...");

            // Set up a specific check for the form with multiple retries
            let retryCount = 0;
            const maxRetries = 10;

            function checkForForm() {
                retryCount++;
                console.log(`Checking for passenger form (attempt ${retryCount}/${maxRetries})...`);

                const form = document.querySelector("app-passenger-input");
                if (form) {
                    console.log("Passenger form found on retry!");
                    fillPassengerDetailsWithData();
                } else if (retryCount < maxRetries) {
                    console.log("Form not found yet, retrying...");
                    setTimeout(checkForForm, 1000);
                } else {
                    console.error("Max retries reached. Form not found.");
                }
            }

            // Start the retry process
            setTimeout(checkForForm, 1000);
            return;
        }

        // Set boarding station if specified
        if (user_data.journey_details.boarding && user_data.journey_details.boarding.length > 0) {
            console.log("Set boarding station " + user_data.journey_details.boarding);
            try {
                const allStrongElements = document.getElementsByTagName("strong");
                const fromStationElements = Array.from(allStrongElements).filter(
                    (e => e.innerText.includes(user_data.journey_details.from.split("-")[0].trim() + " | "))
                );

                if (fromStationElements[0]) {
                    console.log("Clicking from station element");
                    fromStationElements[0].click();
                    addDelay(300);
                } else {
                    console.log("From station element not found");
                }

                const boardingStationElements = Array.from(document.getElementsByTagName("strong")).filter(
                    (e => e.innerText.includes(user_data.journey_details.boarding.split("-")[0].trim()))
                );

                if (boardingStationElements[0]) {
                    console.log("Clicking boarding station element");
                    boardingStationElements[0].click();
                } else {
                    console.log("Boarding station element not found");
                }
            } catch (error) {
                console.error("Error setting boarding station:", error);
            }
        }

        // Set timestamp for form submission timing
        keyCounter = (new Date).getTime();

        // Add additional passengers if needed
        try {
            console.log("Adding additional passengers. Total passengers:", user_data.passenger_details.length);
            let passengerCount = 1; // Start with 1 as one passenger form is already present

            // Add passenger forms until we have enough for all passengers
            while (passengerCount < user_data.passenger_details.length) {
                const addPassengerButtons = document.getElementsByClassName("prenext");
                if (addPassengerButtons && addPassengerButtons.length > 0) {
                    console.log("Clicking add passenger button");
                    addPassengerButtons[0].click();
                    addDelay(200);
                    passengerCount++;
                } else {
                    console.error("Add passenger button not found");
                    break;
                }
            }

            // Add infant forms if needed
            if (user_data.infant_details && user_data.infant_details.length > 0) {
                console.log("Adding infants. Total infants:", user_data.infant_details.length);
                let infantCount = 0;

                while (infantCount < user_data.infant_details.length) {
                    const addInfantButtons = document.getElementsByClassName("prenext");
                    if (addInfantButtons && addInfantButtons.length > 2) {
                        console.log("Clicking add infant button");
                        addInfantButtons[2].click();
                        addDelay(200);
                        infantCount++;
                    } else {
                        console.error("Add infant button not found");
                        break;
                    }
                }
            }
        } catch (error) {
            console.error("Error adding passengers/infants:", error);
        }

        // Get all passenger and infant form elements
        const passengerElements = Array.from(passengerInputElement.querySelectorAll("app-passenger"));
        const infantElements = Array.from(passengerInputElement.querySelectorAll("app-infant"));

        console.log("Found passenger elements:", passengerElements.length);
        console.log("Found infant elements:", infantElements.length);

        // Fill passenger details
        if (passengerElements.length > 0 && user_data.passenger_details && user_data.passenger_details.length > 0) {
            user_data.passenger_details.forEach((passengerData, index) => {
                if (index < passengerElements.length) {
                    try {
                        console.log(`Filling details for passenger ${index + 1}`);

                        // Fill name
                        const nameInput = passengerElements[index].querySelector("p-autocomplete > span > input");
                        if (nameInput) {
                            nameInput.value = passengerData.name;
                            nameInput.dispatchEvent(new Event("input"));
                            console.log(`Set name: ${passengerData.name}`);
                        } else {
                            console.error(`Name input not found for passenger ${index + 1}`);
                        }

                        // Fill age
                        const ageInput = passengerElements[index].querySelector("input[type='number'][formcontrolname='passengerAge']");
                        if (ageInput) {
                            ageInput.value = passengerData.age;
                            ageInput.dispatchEvent(new Event("input"));
                            console.log(`Set age: ${passengerData.age}`);
                        } else {
                            console.error(`Age input not found for passenger ${index + 1}`);
                        }

                        // Fill gender
                        const genderSelect = passengerElements[index].querySelector("select[formcontrolname='passengerGender']");
                        if (genderSelect) {
                            genderSelect.value = passengerData.gender;
                            genderSelect.dispatchEvent(new Event("change"));
                            console.log(`Set gender: ${passengerData.gender}`);
                        } else {
                            console.error(`Gender select not found for passenger ${index + 1}`);
                        }

                        // Fill berth preference
                        const berthSelect = passengerElements[index].querySelector("select[formcontrolname='passengerBerthChoice']");
                        if (berthSelect) {
                            berthSelect.value = passengerData.berth;
                            berthSelect.dispatchEvent(new Event("change"));
                            console.log(`Set berth: ${passengerData.berth}`);
                        } else {
                            console.error(`Berth select not found for passenger ${index + 1}`);
                        }

                        // Fill food choice if available
                        const foodSelect = passengerElements[index].querySelector("select[formcontrolname='passengerFoodChoice']");
                        if (foodSelect && passengerData.food) {
                            foodSelect.value = passengerData.food;
                            foodSelect.dispatchEvent(new Event("change"));
                            console.log(`Set food: ${passengerData.food}`);
                        }

                        // Handle child berth option
                        try {
                            const childBerthCheckbox = passengerElements[index].querySelector("input[type='checkbox'][formcontrolname='childBerthFlag']");
                            if (childBerthCheckbox && passengerData.passengerchildberth) {
                                console.log(`Setting child half seat for passenger ${index + 1}`);
                                childBerthCheckbox.click();
                                addDelay(200);

                                // Handle confirmation dialog if it appears
                                const confirmDialog = document.evaluate(
                                    "//div[contains(text(),'No berth will be allotted for child and')]",
                                    document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null
                                ).singleNodeValue;

                                if (confirmDialog) {
                                    console.log("Clicking OK on child berth dialog");
                                    const okButton = document.querySelector("app-passenger > p-dialog > div > div > div > p-footer > button");
                                    if (okButton) {
                                        okButton.click();
                                        addDelay(200);
                                    }
                                }
                            }
                        } catch (error) {
                            console.error(`Error setting child berth for passenger ${index + 1}:`, error);
                        }
                    } catch (error) {
                        console.error(`Error filling details for passenger ${index + 1}:`, error);
                    }
                }
            });
        } else {
            console.error("No passenger elements found or no passenger data available");
        }

        // Fill infant details
        if (infantElements.length > 0 && user_data.infant_details && user_data.infant_details.length > 0) {
            user_data.infant_details.forEach((infantData, index) => {
                if (index < infantElements.length) {
                    try {
                        console.log(`Filling details for infant ${index + 1}`);

                        // Fill name
                        const nameInput = infantElements[index].querySelector("input#infant-name[name='infant-name']");
                        if (nameInput) {
                            nameInput.value = infantData.name;
                            nameInput.dispatchEvent(new Event("input"));
                            console.log(`Set infant name: ${infantData.name}`);
                        } else {
                            console.error(`Name input not found for infant ${index + 1}`);
                        }

                        // Fill age
                        const ageSelect = infantElements[index].querySelector("select[formcontrolname='age']");
                        if (ageSelect) {
                            ageSelect.value = infantData.age;
                            ageSelect.dispatchEvent(new Event("change"));
                            console.log(`Set infant age: ${infantData.age}`);
                        } else {
                            console.error(`Age select not found for infant ${index + 1}`);
                        }

                        // Fill gender
                        const genderSelect = infantElements[index].querySelector("select[formcontrolname='gender']");
                        if (genderSelect) {
                            genderSelect.value = infantData.gender;
                            genderSelect.dispatchEvent(new Event("change"));
                            console.log(`Set infant gender: ${infantData.gender}`);
                        } else {
                            console.error(`Gender select not found for infant ${index + 1}`);
                        }
                    } catch (error) {
                        console.error(`Error filling details for infant ${index + 1}:`, error);
                    }
                }
            });
        }

        // Fill mobile number
        if (user_data.other_preferences && user_data.other_preferences.mobileNumber && user_data.other_preferences.mobileNumber !== "") {
            try {
                const mobileInput = passengerInputElement.querySelector("input#mobileNumber[formcontrolname='mobileNumber'][name='mobileNumber']");
                if (mobileInput) {
                    mobileInput.value = user_data.other_preferences.mobileNumber;
                    mobileInput.dispatchEvent(new Event("input"));
                    console.log(`Set mobile number: ${user_data.other_preferences.mobileNumber}`);
                } else {
                    console.error("Mobile number input not found");
                }
            } catch (error) {
                console.error("Error setting mobile number:", error);
            }
        }

        // Set payment type
        try {
            const paymentRadioButtons = Array.from(passengerInputElement.querySelectorAll("p-radiobutton[formcontrolname='paymentType'][name='paymentType'] input[type='radio']"));
            if (paymentRadioButtons.length > 0) {
                addDelay(100);
                let paymentValue = "2"; // Default to UPI
                if (!user_data.other_preferences.paymentmethod.includes("UPI")) {
                    paymentValue = "1";
                }

                const paymentButton = paymentRadioButtons.find(button => button.value === paymentValue);
                if (paymentButton) {
                    paymentButton.click();
                    console.log(`Set payment type: ${paymentValue}`);
                } else {
                    console.error("Payment radio button not found");
                }
            } else {
                console.error("Payment radio buttons not found");
            }
        } catch (error) {
            console.error("Error setting payment type:", error);
        }

        // Set auto-upgradation preference
        try {
            const autoUpgradeCheckbox = passengerInputElement.querySelector("input#autoUpgradation[type='checkbox'][formcontrolname='autoUpgradationSelected']");
            if (autoUpgradeCheckbox && user_data.other_preferences.hasOwnProperty("autoUpgradation")) {
                autoUpgradeCheckbox.checked = user_data.other_preferences.autoUpgradation ?? false;
                console.log(`Set auto-upgradation: ${autoUpgradeCheckbox.checked}`);
            }
        } catch (error) {
            console.error("Error setting auto-upgradation:", error);
        }

        // Set confirm berths preference
        try {
            const confirmBerthsCheckbox = passengerInputElement.querySelector("input#confirmberths[type='checkbox'][formcontrolname='bookOnlyIfCnf']");
            if (confirmBerthsCheckbox && user_data.other_preferences.hasOwnProperty("confirmberths")) {
                confirmBerthsCheckbox.checked = user_data.other_preferences.confirmberths ?? false;
                console.log(`Set confirm berths: ${confirmBerthsCheckbox.checked}`);
            }
        } catch (error) {
            console.error("Error setting confirm berths:", error);
        }

        // Set travel insurance preference
        try {
            const insuranceRadioButtons = Array.from(passengerInputElement.querySelectorAll("p-radiobutton[formcontrolname='travelInsuranceOpted'] input[type='radio'][name='travelInsuranceOpted-0']"));
            if (insuranceRadioButtons.length > 0) {
                addDelay(200);
                const insuranceValue = user_data.travel_preferences.travelInsuranceOpted === "yes" ? "true" : "false";

                const insuranceButton = insuranceRadioButtons.find(button => button.value === insuranceValue);
                if (insuranceButton) {
                    insuranceButton.click();
                    console.log(`Set travel insurance: ${insuranceValue}`);
                } else {
                    console.error("Insurance radio button not found");
                }
            } else {
                console.error("Insurance radio buttons not found");
            }
        } catch (error) {
            console.error("Error setting travel insurance:", error);
        }

        // Set coach preference and reservation choice
        try {
            // Set preferred coach ID
            const coachInput = passengerInputElement.querySelector("input[formcontrolname='coachId']");
            if (coachInput && user_data.travel_preferences.hasOwnProperty("prefcoach") && user_data.travel_preferences.prefcoach.trim().length > 0) {
                coachInput.value = user_data.travel_preferences.prefcoach;
                console.log(`Set preferred coach ID: ${user_data.travel_preferences.prefcoach}`);
            }

            // Set reservation choice
            const reservationDropdown = passengerInputElement.querySelector("p-dropdown[formcontrolname='reservationChoice']");
            if (reservationDropdown && user_data.travel_preferences.hasOwnProperty("reservationchoice") && !user_data.travel_preferences.reservationchoice.includes("Reservation Choice")) {
                console.log(`Setting reservation choice: ${user_data.travel_preferences.reservationchoice}`);

                // Click to open dropdown
                const dropdownButton = reservationDropdown.querySelector("div > div[role='button']");
                if (dropdownButton) {
                    dropdownButton.click();
                    addDelay(300);

                    // Find and click the matching option
                    const options = Array.from(reservationDropdown.querySelectorAll("ul li"));
                    const matchingOption = options.find(option => option.innerText === user_data.travel_preferences.reservationchoice);

                    if (matchingOption) {
                        matchingOption.click();
                        console.log("Selected reservation choice option");
                    } else {
                        console.error("Reservation choice option not found");
                    }
                } else {
                    console.error("Reservation choice dropdown button not found");
                }
            }
        } catch (error) {
            console.error("Error setting coach preference or reservation choice:", error);
        }

        console.log("All passenger details filled successfully");

        // Submit the form
        submitPassengerDetailsForm(passengerInputElement);

    } catch (error) {
        console.error("Error in fillPassengerDetailsWithData:", error);
        // Try again after a delay
        setTimeout(fillPassengerDetailsWithData, 2000);
    }
}

function submitPassengerDetailsForm(e) {
    if (console.log("passenger_filling_completed"), window.scrollBy(0, 600, "smooth"), user_data.other_preferences.hasOwnProperty("psgManual") && user_data.other_preferences.psgManual) alert("Manually submit the passenger page.");
    else var t = setInterval((function () {
        var o = (new Date).getTime();
        keyCounter > 0 && o - keyCounter > 2e3 && (clearInterval(t), e.querySelector("#psgn-form > form div > button.train_Search.btnDefault[type='submit']")?.click(), window.scrollBy(0, 600, "smooth"))
    }), 500)
}

function continueScript() {
    const e = document.querySelector("body > app-root > app-home > div.header-fix > app-header > div.col-sm-12.h_container > div.text-center.h_main_div > div.row.col-sm-12.h_head1 > a.search_btn.loginText.ng-star-inserted");

    if (window.location.href.includes("train-search")) {
        if ("LOGOUT" === e.innerText.trim().toUpperCase()) {
            loadJourneyDetails();
        } else if ("LOGIN" === e.innerText.trim().toUpperCase()) {
            e.click();
            loadLoginDetails();
        }
    } else if (window.location.href.includes("nget/booking/train-list")) {
        console.log("Train list page detected in continueScript, calling selectJourney()");
        // Wait a bit for the page to fully load before selecting the journey
        setTimeout(() => {
            selectJourney(); // Will handle loading user data if needed
        }, 2000);
    } else if (window.location.href.includes("nget/booking/psgninput")) {
        console.log("Passenger input page detected in continueScript");

        // Function to check if the page is ready for filling
        function checkPageReadyAndFill() {
            // Check if loader is visible
            const loader = document.querySelector("#loaderP");
            if (loader && window.getComputedStyle(loader).display !== "none") {
                console.log("continueScript: Loader is still visible. Waiting before filling passenger details...");
                setTimeout(checkPageReadyAndFill, 1000);
                return;
            }

            // Check if the passenger form is present
            const passengerForm = document.querySelector("app-passenger-input");
            if (!passengerForm) {
                console.log("continueScript: Passenger form not yet available. Waiting...");
                setTimeout(checkPageReadyAndFill, 1000);
                return;
            }

            // Check if the first passenger name field is present
            const nameField = document.querySelector("app-passenger p-autocomplete > span > input");
            if (!nameField) {
                console.log("continueScript: Passenger name field not yet available. Waiting...");
                setTimeout(checkPageReadyAndFill, 1000);
                return;
            }

            console.log("continueScript: Page is ready for filling passenger details!");
            fillPassengerDetails(); // Will handle loading user data if needed
        }

        // Start checking if the page is ready
        checkPageReadyAndFill();
    } else {
        console.log("Nothing to do - Current URL: " + window.location.href);
    }
}
// Unused plan validation function removed
// Set up a URL change listener to detect navigation to important pages
let lastUrl = location.href;
new MutationObserver(() => {
    const currentUrl = location.href;
    if (currentUrl !== lastUrl) {
        console.log(`URL changed from ${lastUrl} to ${currentUrl}`);
        lastUrl = currentUrl;

        // Handle train list page
        if (currentUrl.includes("nget/booking/train-list")) {
            console.log("Train list page detected via URL change, calling selectJourney");
            // Always call selectJourney which will handle loading user data if needed
            setTimeout(() => {
                selectJourney();
            }, 2000);
        }

        // Handle passenger input page
        if (currentUrl.includes("nget/booking/psgninput")) {
            console.log("Passenger input page detected via URL change");

            // Function to check if the page is ready for filling
            function checkPageReadyAndFill() {
                // Check if loader is visible
                const loader = document.querySelector("#loaderP");
                if (loader && window.getComputedStyle(loader).display !== "none") {
                    console.log("Loader is still visible. Waiting before filling passenger details...");
                    setTimeout(checkPageReadyAndFill, 1000);
                    return;
                }

                // Check if the passenger form is present
                const passengerForm = document.querySelector("app-passenger-input");
                if (!passengerForm) {
                    console.log("Passenger form not yet available. Waiting...");
                    setTimeout(checkPageReadyAndFill, 1000);
                    return;
                }

                // Check if the first passenger name field is present
                const nameField = document.querySelector("app-passenger p-autocomplete > span > input");
                if (!nameField) {
                    console.log("Passenger name field not yet available. Waiting...");
                    setTimeout(checkPageReadyAndFill, 1000);
                    return;
                }

                console.log("Page is ready for filling passenger details!");
                fillPassengerDetails();
            }

            // Start checking if the page is ready
            checkPageReadyAndFill();

            // Also set up backup attempts with longer delays
            setTimeout(() => {
                console.log("Backup attempt to fill passenger details (3s)");
                fillPassengerDetails();
            }, 3000);

            setTimeout(() => {
                console.log("Backup attempt to fill passenger details (6s)");
                fillPassengerDetails();
            }, 6000);

            setTimeout(() => {
                console.log("Final backup attempt to fill passenger details (10s)");
                fillPassengerDetails();
            }, 10000);
        }

        // Handle review booking page
        if (currentUrl.includes("nget/booking/reviewBooking")) {
            console.log("Review booking page detected via URL change");

            // Function to check if the page is ready for captcha filling
            function checkPageReadyAndFillCaptcha() {
                console.log("Checking if review booking page is ready for captcha filling");

                // Check if loader is visible
                const loader = document.querySelector("#loaderP");
                if (loader && window.getComputedStyle(loader).display !== "none") {
                    console.log("Loader is still visible on review page. Waiting...");
                    setTimeout(checkPageReadyAndFillCaptcha, 1000);
                    return;
                }

                // Try multiple selectors for the captcha input field
                const captchaInputSelectors = [
                    "#captcha",
                    "input[name='captcha']",
                    "input[formcontrolname='captcha']",
                    "#divMain input[type='text'][autocomplete='off']",
                    "input.captchaTextBox"
                ];

                let captchaInput = null;

                // Try each selector until we find a match
                for (const selector of captchaInputSelectors) {
                    const input = document.querySelector(selector);
                    if (input) {
                        captchaInput = input;
                        console.log(`Found captcha input with selector: ${selector}`);
                        break;
                    }
                }

                if (!captchaInput) {
                    console.log("Captcha input not found yet. Waiting...");
                    setTimeout(checkPageReadyAndFillCaptcha, 1000);
                    return;
                }

                console.log("Review booking page is ready for captcha filling");

                // Scroll to captcha field
                try {
                    captchaInput.scrollIntoView({
                        behavior: "smooth",
                        block: "center",
                        inline: "nearest"
                    });
                    console.log("Scrolled to captcha field");
                } catch (error) {
                    console.error("Error scrolling to captcha field:", error);
                }

                // Handle captcha
                if (user_data.other_preferences && user_data.other_preferences.autoCaptcha) {
                    console.log("Auto captcha is enabled, calling getCaptcha()");
                    // Use the Ninjas API for captcha
                    setTimeout(() => {
                        getCaptcha();
                    }, 500);
                } else {
                    console.log("Manual captcha filling");
                    let placeholderText = "X";
                    captchaInput.value = placeholderText;
                    captchaInput.dispatchEvent(new Event("input"));
                    captchaInput.dispatchEvent(new Event("change"));
                    captchaInput.focus();
                }
            }

            // Check if we have user_data
            if (!user_data || Object.keys(user_data).length === 0) {
                console.log("User data not available for review booking, loading from storage...");

                // Load user data from storage and then continue
                chrome.storage.local.get(null, function(data) {
                    console.log("User data loaded from storage for review booking");
                    user_data = data;

                    // Start checking if the page is ready
                    checkPageReadyAndFillCaptcha();
                });
            } else {
                // We already have user data, proceed
                checkPageReadyAndFillCaptcha();
            }

            // Also set up backup attempts with longer delays
            setTimeout(() => {
                console.log("Backup attempt to fill captcha (3s)");
                getCaptcha();
            }, 3000);

            setTimeout(() => {
                console.log("Backup attempt to fill captcha (6s)");
                getCaptcha();
            }, 6000);

            setTimeout(() => {
                console.log("Final backup attempt to fill captcha (10s)");
                getCaptcha();
            }, 10000);
        }
    }
}).observe(document, {subtree: true, childList: true});

window.onload = function () {
    console.log("Window loaded, initializing extension...");
    console.log("Current URL at load:", window.location.href);

    // Set up the repeater to keep the listener alive
    setInterval(function () {
        console.log("Repeater"), statusUpdate("Keep listener alive.")
    }, 15000);

    // First, get the user data from storage
    chrome.storage.local.get(null, function(data) {
        console.log("User data loaded from storage");
        user_data = data;

        // Check if we're already on the train list page
        if (window.location.href.includes("nget/booking/train-list")) {
            console.log("Already on train list page, calling selectJourney directly");
            setTimeout(() => {
                selectJourney(); // Will handle loading user data if needed
            }, 2000);
            return;
        }

        // Check if we're already on the passenger input page
        if (window.location.href.includes("nget/booking/psgninput")) {
            console.log("Already on passenger input page in window.onload");

            // Function to check if the page is ready for filling
            function checkPageReadyAndFill() {
                // Check if loader is visible
                const loader = document.querySelector("#loaderP");
                if (loader && window.getComputedStyle(loader).display !== "none") {
                    console.log("window.onload: Loader is still visible. Waiting before filling passenger details...");
                    setTimeout(checkPageReadyAndFill, 1000);
                    return;
                }

                // Check if the passenger form is present
                const passengerForm = document.querySelector("app-passenger-input");
                if (!passengerForm) {
                    console.log("window.onload: Passenger form not yet available. Waiting...");
                    setTimeout(checkPageReadyAndFill, 1000);
                    return;
                }

                // Check if the first passenger name field is present
                const nameField = document.querySelector("app-passenger p-autocomplete > span > input");
                if (!nameField) {
                    console.log("window.onload: Passenger name field not yet available. Waiting...");
                    setTimeout(checkPageReadyAndFill, 1000);
                    return;
                }

                console.log("window.onload: Page is ready for filling passenger details!");
                fillPassengerDetails(); // Will handle loading user data if needed
            }

            // Start checking if the page is ready
            checkPageReadyAndFill();
            return;
        }

        // Check if we're already on the review booking page
        if (window.location.href.includes("nget/booking/reviewBooking")) {
            console.log("Already on review booking page in window.onload");

            // Function to check if the page is ready for captcha filling
            function checkPageReadyAndFillCaptcha() {
                console.log("window.onload: Checking if review booking page is ready for captcha filling");

                // Check if loader is visible
                const loader = document.querySelector("#loaderP");
                if (loader && window.getComputedStyle(loader).display !== "none") {
                    console.log("window.onload: Loader is still visible on review page. Waiting...");
                    setTimeout(checkPageReadyAndFillCaptcha, 1000);
                    return;
                }

                // Try multiple selectors for the captcha input field
                const captchaInputSelectors = [
                    "#captcha",
                    "input[name='captcha']",
                    "input[formcontrolname='captcha']",
                    "#divMain input[type='text'][autocomplete='off']",
                    "input.captchaTextBox"
                ];

                let captchaInput = null;

                // Try each selector until we find a match
                for (const selector of captchaInputSelectors) {
                    const input = document.querySelector(selector);
                    if (input) {
                        captchaInput = input;
                        console.log(`window.onload: Found captcha input with selector: ${selector}`);
                        break;
                    }
                }

                if (!captchaInput) {
                    console.log("window.onload: Captcha input not found yet. Waiting...");
                    setTimeout(checkPageReadyAndFillCaptcha, 1000);
                    return;
                }

                console.log("window.onload: Review booking page is ready for captcha filling");

                // Scroll to captcha field
                try {
                    captchaInput.scrollIntoView({
                        behavior: "smooth",
                        block: "center",
                        inline: "nearest"
                    });
                    console.log("window.onload: Scrolled to captcha field");
                } catch (error) {
                    console.error("window.onload: Error scrolling to captcha field:", error);
                }

                // Handle captcha
                if (user_data.other_preferences && user_data.other_preferences.autoCaptcha) {
                    console.log("window.onload: Auto captcha is enabled, calling getCaptcha()");
                    // Use the Ninjas API for captcha
                    setTimeout(() => {
                        getCaptcha();
                    }, 500);
                } else {
                    console.log("window.onload: Manual captcha filling");
                    let placeholderText = "X";
                    captchaInput.value = placeholderText;
                    captchaInput.dispatchEvent(new Event("input"));
                    captchaInput.dispatchEvent(new Event("change"));
                    captchaInput.focus();
                }
            }

            // Start checking if the page is ready
            checkPageReadyAndFillCaptcha();
            return;
        }

        // Set up observer for login/logout state
        const headerElement = document.querySelector("body > app-root > app-home > div.header-fix > app-header > div.col-sm-12.h_container > div.text-center.h_main_div > div.row.col-sm-12.h_head1 ");

        if (headerElement) {
            console.log("Setting up header observer");
            new MutationObserver((mutations, observer) => {
                const logoutAdded = mutations.filter(mutation =>
                    mutation.type === "childList" &&
                    mutation.addedNodes.length > 0 &&
                    [...mutation.addedNodes].filter(node =>
                        node?.innerText?.trim()?.toUpperCase() === "LOGOUT"
                    ).length > 0
                ).length > 0;

                if (logoutAdded) {
                    console.log("LOGOUT detected, disconnecting observer and loading journey details");
                    observer.disconnect();
                    loadJourneyDetails();
                } else {
                    console.log("LOGIN detected, clicking and loading login details");
                    headerElement.click();
                    loadLoginDetails();
                }
            }).observe(headerElement, {
                attributes: false,
                childList: true,
                subtree: false
            });
        } else {
            console.warn("Header element not found");
        }

        // Continue with the script based on current page
        continueScript();
    });
};

// Add a direct call to check for important pages after a short delay
setTimeout(() => {
    // Check for train list page
    if (window.location.href.includes("nget/booking/train-list")) {
        console.log("Train list page detected via delayed check, calling selectJourney");
        selectJourney(); // Will handle loading user data if needed
    }

    // Check for passenger input page
    if (window.location.href.includes("nget/booking/psgninput")) {
        console.log("Passenger input page detected via delayed check");

        // Function to check if the page is ready for filling
        function checkPageReadyAndFill() {
            // Check if loader is visible
            const loader = document.querySelector("#loaderP");
            if (loader && window.getComputedStyle(loader).display !== "none") {
                console.log("Delayed check: Loader is still visible. Waiting before filling passenger details...");
                setTimeout(checkPageReadyAndFill, 1000);
                return;
            }

            // Check if the passenger form is present
            const passengerForm = document.querySelector("app-passenger-input");
            if (!passengerForm) {
                console.log("Delayed check: Passenger form not yet available. Waiting...");
                setTimeout(checkPageReadyAndFill, 1000);
                return;
            }

            // Check if the first passenger name field is present
            const nameField = document.querySelector("app-passenger p-autocomplete > span > input");
            if (!nameField) {
                console.log("Delayed check: Passenger name field not yet available. Waiting...");
                setTimeout(checkPageReadyAndFill, 1000);
                return;
            }

            console.log("Delayed check: Page is ready for filling passenger details!");
            fillPassengerDetails(); // Will handle loading user data if needed
        }

        // Start checking if the page is ready
        checkPageReadyAndFill();
    }

    // Check for review booking page
    if (window.location.href.includes("nget/booking/reviewBooking")) {
        console.log("Review booking page detected via delayed check");

        // Function to check if the page is ready for captcha filling
        function checkPageReadyAndFillCaptcha() {
            console.log("Delayed check: Checking if review booking page is ready for captcha filling");

            // Check if loader is visible
            const loader = document.querySelector("#loaderP");
            if (loader && window.getComputedStyle(loader).display !== "none") {
                console.log("Delayed check: Loader is still visible on review page. Waiting...");
                setTimeout(checkPageReadyAndFillCaptcha, 1000);
                return;
            }

            // Try multiple selectors for the captcha input field
            const captchaInputSelectors = [
                "#captcha",
                "input[name='captcha']",
                "input[formcontrolname='captcha']",
                "#divMain input[type='text'][autocomplete='off']",
                "input.captchaTextBox"
            ];

            let captchaInput = null;

            // Try each selector until we find a match
            for (const selector of captchaInputSelectors) {
                const input = document.querySelector(selector);
                if (input) {
                    captchaInput = input;
                    console.log(`Delayed check: Found captcha input with selector: ${selector}`);
                    break;
                }
            }

            if (!captchaInput) {
                console.log("Delayed check: Captcha input not found yet. Waiting...");
                setTimeout(checkPageReadyAndFillCaptcha, 1000);
                return;
            }

            console.log("Delayed check: Review booking page is ready for captcha filling");

            // Scroll to captcha field
            try {
                captchaInput.scrollIntoView({
                    behavior: "smooth",
                    block: "center",
                    inline: "nearest"
                });
                console.log("Delayed check: Scrolled to captcha field");
            } catch (error) {
                console.error("Delayed check: Error scrolling to captcha field:", error);
            }

            // Handle captcha
            if (user_data.other_preferences && user_data.other_preferences.autoCaptcha) {
                console.log("Delayed check: Auto captcha is enabled, calling getCaptcha()");
                // Use the Ninjas API for captcha
                setTimeout(() => {
                    getCaptcha();
                }, 500);
            } else {
                console.log("Delayed check: Manual captcha filling");
                let placeholderText = "X";
                captchaInput.value = placeholderText;
                captchaInput.dispatchEvent(new Event("input"));
                captchaInput.dispatchEvent(new Event("change"));
                captchaInput.focus();
            }
        }

        // Start checking if the page is ready
        checkPageReadyAndFillCaptcha();
    }
}, 3000);

// Add a direct DOM content loaded listener for the passenger input page
document.addEventListener('DOMContentLoaded', function() {
    console.log("DOMContentLoaded event fired, current URL:", window.location.href);

    if (window.location.href.includes("nget/booking/psgninput")) {
        console.log("Passenger input page detected via DOMContentLoaded");

        // Function to check if the page is ready for filling
        function checkPageReadyAndFill() {
            // Check if loader is visible
            const loader = document.querySelector("#loaderP");
            if (loader && window.getComputedStyle(loader).display !== "none") {
                console.log("DOMContentLoaded: Loader is still visible. Waiting before filling passenger details...");
                setTimeout(checkPageReadyAndFill, 1000);
                return;
            }

            // Check if the passenger form is present
            const passengerForm = document.querySelector("app-passenger-input");
            if (!passengerForm) {
                console.log("DOMContentLoaded: Passenger form not yet available. Waiting...");
                setTimeout(checkPageReadyAndFill, 1000);
                return;
            }

            // Check if the first passenger name field is present
            const nameField = document.querySelector("app-passenger p-autocomplete > span > input");
            if (!nameField) {
                console.log("DOMContentLoaded: Passenger name field not yet available. Waiting...");
                setTimeout(checkPageReadyAndFill, 1000);
                return;
            }

            console.log("DOMContentLoaded: Page is ready for filling passenger details!");
            fillPassengerDetails();
        }

        // Start checking if the page is ready
        checkPageReadyAndFill();

        // Also set up a backup attempt with a longer delay
        setTimeout(() => {
            console.log("DOMContentLoaded: Backup attempt to fill passenger details");
            fillPassengerDetails();
        }, 5000);
    }
});

// Add a specific mutation observer to detect when the passenger input form is added to the DOM
const passengerFormObserver = new MutationObserver((mutations) => {
    for (const mutation of mutations) {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            for (const node of mutation.addedNodes) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    // Check if this is the passenger input form or contains it
                    if (node.querySelector &&
                        (node.tagName === 'APP-PASSENGER-INPUT' ||
                         node.querySelector('app-passenger-input'))) {
                        console.log("Passenger input form detected via mutation observer");

                        // Function to check if the page is fully ready for filling
                        function checkFormReadyAndFill() {
                            // Check if loader is visible
                            const loader = document.querySelector("#loaderP");
                            if (loader && window.getComputedStyle(loader).display !== "none") {
                                console.log("MutationObserver: Loader is still visible. Waiting before filling passenger details...");
                                setTimeout(checkFormReadyAndFill, 1000);
                                return;
                            }

                            // Check if the first passenger name field is present
                            const nameField = document.querySelector("app-passenger p-autocomplete > span > input");
                            if (!nameField) {
                                console.log("MutationObserver: Passenger name field not yet available. Waiting...");
                                setTimeout(checkFormReadyAndFill, 1000);
                                return;
                            }

                            console.log("MutationObserver: Form is fully ready for filling passenger details!");
                            fillPassengerDetails();
                        }

                        // Start checking if the form is fully ready
                        setTimeout(checkFormReadyAndFill, 500);

                        // No need to keep observing once we've found the form
                        passengerFormObserver.disconnect();
                        return;
                    }
                }
            }
        }
    }
});

// Start observing if we're on the passenger input page
if (window.location.href.includes("nget/booking/psgninput")) {
    console.log("Starting passenger form observer");
    passengerFormObserver.observe(document.body, {
        childList: true,
        subtree: true
    });
}