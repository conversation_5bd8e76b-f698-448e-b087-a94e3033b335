module.exports = {
  extends: [
    'react-app',
    'react-app/jest'
  ],
  rules: {
    // Disable specific rules that are causing build failures
    'no-unused-vars': 'warn',
    'react-hooks/exhaustive-deps': 'warn'
  },
  overrides: [
    {
      files: ['**/*.js', '**/*.jsx'],
      rules: {
        // In production builds, treat these as warnings instead of errors
        'no-unused-vars': process.env.NODE_ENV === 'production' ? 'warn' : 'error',
        'react-hooks/exhaustive-deps': process.env.NODE_ENV === 'production' ? 'warn' : 'error'
      }
    }
  ]
};
