<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Google_Client;

class AuthController extends Controller
{
    /**
     * Handle Google OAuth login for Chrome extension
     */
    public function googleLogin(Request $request)
    {
        try {
            $request->validate([
                'access_token' => 'required|string',
            ]);

            // Verify Google token
            $client = new Google_Client();
            $client->setClientId(config('services.google.client_id'));

            $payload = $client->verifyIdToken($request->access_token);

            if (!$payload) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid Google token'
                ], 401);
            }

            // Find or create user
            $user = User::where('google_id', $payload['sub'])
                       ->orWhere('email', $payload['email'])
                       ->first();

            if (!$user) {
                $user = User::create([
                    'name' => $payload['name'],
                    'email' => $payload['email'],
                    'google_id' => $payload['sub'],
                    'picture' => $payload['picture'] ?? null,
                    'email_verified_at' => now(),
                    'api_key' => Str::random(32),
                ]);
            } else {
                // Update Google ID if not set
                if (!$user->google_id) {
                    $user->update(['google_id' => $payload['sub']]);
                }
                $user->update(['last_login' => now()]);
            }

            // Create API token
            $token = $user->createToken('chrome-extension')->plainTextToken;

            return response()->json([
                'success' => true,
                'message' => 'Login successful',
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'picture' => $user->picture,
                    'available_tickets' => $user->available_tickets,
                ],
                'token' => $token,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle Facebook OAuth login for Chrome extension
     */
    public function facebookLogin(Request $request)
    {
        try {
            $request->validate([
                'access_token' => 'required|string',
                'user_data' => 'required|array',
            ]);

            $userData = $request->user_data;

            // Find or create user
            $user = User::where('facebook_id', $userData['id'])
                       ->orWhere('email', $userData['email'])
                       ->first();

            if (!$user) {
                $user = User::create([
                    'name' => $userData['name'],
                    'email' => $userData['email'],
                    'facebook_id' => $userData['id'],
                    'picture' => $userData['picture']['data']['url'] ?? null,
                    'email_verified_at' => now(),
                    'api_key' => Str::random(32),
                ]);
            } else {
                // Update Facebook ID if not set
                if (!$user->facebook_id) {
                    $user->update(['facebook_id' => $userData['id']]);
                }
                $user->update(['last_login' => now()]);
            }

            // Create API token
            $token = $user->createToken('chrome-extension')->plainTextToken;

            return response()->json([
                'success' => true,
                'message' => 'Login successful',
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'picture' => $user->picture,
                    'available_tickets' => $user->available_tickets,
                ],
                'token' => $token,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get authenticated user info
     */
    public function me(Request $request)
    {
        $user = $request->user();

        return response()->json([
            'success' => true,
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'picture' => $user->picture,
                'available_tickets' => $user->available_tickets,
                'status' => $user->status,
                'last_login' => $user->last_login,
            ]
        ]);
    }

    /**
     * Logout user (revoke token)
     */
    public function logout(Request $request)
    {
        $request->user()->currentAccessToken()->delete();

        return response()->json([
            'success' => true,
            'message' => 'Logged out successfully'
        ]);
    }
}
