import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { adminAPI, getCurrentAdmin, adminLogout, handleApiError } from '../services/api';
import '../styles/AdminPanel.css';

const AdminPanel = () => {
  const [admin] = useState(getCurrentAdmin());
  const [stats, setStats] = useState({});
  const [recentUsers, setRecentUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch admin stats
      const statsResponse = await adminAPI.getStats();
      setStats(statsResponse.data.stats || {});
      
      // Fetch recent users (first 5)
      const usersResponse = await adminAPI.getUsers({ limit: 5, page: 1 });
      setRecentUsers(usersResponse.data.users || []);
      
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setError(handleApiError(error));
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    adminLogout();
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="admin-loading">
        <div className="loading-spinner"></div>
        <p>Loading admin dashboard...</p>
      </div>
    );
  }

  return (
    <div className="admin-panel">
      {/* Header */}
      <header className="admin-header">
        <div className="header-content">
          <div className="admin-info">
            <h1>Admin Dashboard</h1>
            <p>Welcome back, {admin?.name}</p>
          </div>
          <div className="header-actions">
            <button 
              className="users-btn"
              onClick={() => navigate('/admin/users')}
            >
              Manage Users
            </button>
            <button className="logout-btn" onClick={handleLogout}>
              Logout
            </button>
          </div>
        </div>
      </header>

      <div className="admin-content">
        {error && <div className="alert alert-error">{error}</div>}

        {/* Stats Cards */}
        <section className="stats-section">
          <div className="stats-grid">
            <div className="stat-card">
              <div className="stat-icon">👥</div>
              <div className="stat-content">
                <h3>{stats.totalUsers || 0}</h3>
                <p>Total Users</p>
              </div>
            </div>
            
            <div className="stat-card">
              <div className="stat-icon">✅</div>
              <div className="stat-content">
                <h3>{stats.activeUsers || 0}</h3>
                <p>Active Users</p>
              </div>
            </div>
            
            <div className="stat-card">
              <div className="stat-icon">🎫</div>
              <div className="stat-content">
                <h3>{stats.totalTickets || 0}</h3>
                <p>Total Credits Sold</p>
              </div>
            </div>
            
            <div className="stat-card">
              <div className="stat-icon">📊</div>
              <div className="stat-content">
                <h3>{stats.totalBookings || 0}</h3>
                <p>Total Bookings</p>
              </div>
            </div>
          </div>
        </section>

        {/* Recent Users */}
        <section className="recent-users-section">
          <div className="section-header">
            <h2>Recent Users</h2>
            <button 
              className="view-all-btn"
              onClick={() => navigate('/admin/users')}
            >
              View All Users
            </button>
          </div>
          
          {recentUsers.length > 0 ? (
            <div className="users-table">
              <table>
                <thead>
                  <tr>
                    <th>User</th>
                    <th>Email</th>
                    <th>Credits</th>
                    <th>Status</th>
                    <th>Joined</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {recentUsers.map((user) => (
                    <tr key={user._id}>
                      <td>
                        <div className="user-cell">
                          <img 
                            src={user.picture || '/default-avatar.png'} 
                            alt={user.name}
                            className="user-avatar-small"
                          />
                          <span>{user.name}</span>
                        </div>
                      </td>
                      <td>{user.email}</td>
                      <td>
                        <span className="credits-badge">
                          {user.availableTickets}
                        </span>
                      </td>
                      <td>
                        <span className={`status-badge ${user.status}`}>
                          {user.status}
                        </span>
                      </td>
                      <td>{formatDate(user.createdAt)}</td>
                      <td>
                        <button 
                          className="action-btn"
                          onClick={() => navigate(`/admin/users/${user._id}`)}
                        >
                          View Details
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="no-data">
              <p>No users found.</p>
            </div>
          )}
        </section>

        {/* Quick Actions */}
        <section className="quick-actions-section">
          <h2>Quick Actions</h2>
          <div className="actions-grid">
            <div className="action-card">
              <div className="action-icon">👥</div>
              <h3>User Management</h3>
              <p>View, edit, and manage user accounts</p>
              <button 
                className="action-button"
                onClick={() => navigate('/admin/users')}
              >
                Manage Users
              </button>
            </div>
            
            <div className="action-card">
              <div className="action-icon">💳</div>
              <h3>Credit Management</h3>
              <p>Add or remove credits from user accounts</p>
              <button 
                className="action-button"
                onClick={() => navigate('/admin/users')}
              >
                Manage Credits
              </button>
            </div>
            
            <div className="action-card">
              <div className="action-icon">📊</div>
              <h3>Analytics</h3>
              <p>View detailed analytics and reports</p>
              <button 
                className="action-button"
                onClick={() => setError('Analytics feature coming soon!')}
              >
                View Analytics
              </button>
            </div>
            
            <div className="action-card">
              <div className="action-icon">⚙️</div>
              <h3>System Settings</h3>
              <p>Configure system settings and preferences</p>
              <button 
                className="action-button"
                onClick={() => setError('Settings feature coming soon!')}
              >
                Settings
              </button>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default AdminPanel;
