#!/usr/bin/env node

/**
 * IRCTC Routes Tester - Node.js Script
 * Tests all backend routes and provides detailed results
 */

const https = require('https');
const http = require('http');

const BACKEND_URL = 'https://ex-irctc.onrender.com';
const COLORS = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

const routes = [
    { path: '/', method: 'GET', description: 'Root endpoint', expectedStatus: 200 },
    { path: '/health', method: 'GET', description: 'Health check', expectedStatus: 200 },
    { path: '/api', method: 'GET', description: 'API documentation', expectedStatus: 200 },
    { path: '/api/auth/google', method: 'POST', description: 'Google OAuth', expectedStatus: 400, body: '{}' },
    { path: '/api/users/credits', method: 'GET', description: 'User credits', expectedStatus: 401 },
    { path: '/api/tickets/count', method: 'GET', description: 'Ticket count', expectedStatus: 401 },
    { path: '/api/payment/create-order', method: 'POST', description: 'Create payment order', expectedStatus: 401, body: '{}' },
    { path: '/api/admin/login', method: 'POST', description: 'Admin login', expectedStatus: 400, body: '{}' },
    { path: '/login', method: 'GET', description: 'Login page (HTML)', expectedStatus: 200 },
    { path: '/dashboard', method: 'GET', description: 'Dashboard page (HTML)', expectedStatus: 200 },
    { path: '/nonexistent', method: 'GET', description: 'Non-existent route', expectedStatus: 404 }
];

function makeRequest(url, options) {
    return new Promise((resolve, reject) => {
        const protocol = url.startsWith('https:') ? https : http;
        
        const req = protocol.request(url, options, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                resolve({
                    status: res.statusCode,
                    headers: res.headers,
                    data: data
                });
            });
        });

        req.on('error', reject);
        
        if (options.body) {
            req.write(options.body);
        }
        
        req.end();
    });
}

function log(message, color = 'reset') {
    console.log(`${COLORS[color]}${message}${COLORS.reset}`);
}

function formatResponse(data, maxLength = 200) {
    try {
        const parsed = JSON.parse(data);
        return JSON.stringify(parsed, null, 2);
    } catch {
        return data.length > maxLength ? data.substring(0, maxLength) + '...' : data;
    }
}

async function testRoute(route) {
    const url = `${BACKEND_URL}${route.path}`;
    const options = {
        method: route.method,
        headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'IRCTC-Route-Tester/1.0'
        }
    };

    if (route.body) {
        options.body = route.body;
    }

    try {
        log(`\n🔄 Testing: ${route.method} ${route.path}`, 'cyan');
        log(`   Description: ${route.description}`, 'blue');
        
        const response = await makeRequest(url, options);
        const isExpectedStatus = response.status === route.expectedStatus;
        
        if (isExpectedStatus) {
            log(`   ✅ Status: ${response.status} (Expected: ${route.expectedStatus})`, 'green');
        } else {
            log(`   ⚠️  Status: ${response.status} (Expected: ${route.expectedStatus})`, 'yellow');
        }
        
        // Show response preview
        const preview = formatResponse(response.data, 150);
        if (preview.length > 0) {
            log(`   📄 Response preview:`, 'magenta');
            console.log(`      ${preview.split('\n').join('\n      ')}`);
        }
        
        return {
            route: route.path,
            method: route.method,
            status: response.status,
            expected: route.expectedStatus,
            passed: isExpectedStatus,
            response: response.data
        };
        
    } catch (error) {
        log(`   ❌ Error: ${error.message}`, 'red');
        return {
            route: route.path,
            method: route.method,
            status: 'ERROR',
            expected: route.expectedStatus,
            passed: false,
            error: error.message
        };
    }
}

async function runTests() {
    log('🚀 IRCTC Backend Routes Tester', 'bright');
    log('=' .repeat(50), 'cyan');
    log(`Testing backend: ${BACKEND_URL}`, 'blue');
    log(`Total routes to test: ${routes.length}`, 'blue');
    
    const results = [];
    let passed = 0;
    let failed = 0;
    
    for (let i = 0; i < routes.length; i++) {
        const route = routes[i];
        log(`\n[${i + 1}/${routes.length}]`, 'bright');
        
        const result = await testRoute(route);
        results.push(result);
        
        if (result.passed) {
            passed++;
        } else {
            failed++;
        }
        
        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // Summary
    log('\n' + '='.repeat(50), 'cyan');
    log('📊 TEST SUMMARY', 'bright');
    log('='.repeat(50), 'cyan');
    log(`✅ Passed: ${passed}`, 'green');
    log(`❌ Failed: ${failed}`, 'red');
    log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`, 'blue');
    
    if (passed >= routes.length * 0.8) {
        log('\n🎉 Great! Most routes are working correctly.', 'green');
    } else {
        log('\n⚠️  Some routes need attention.', 'yellow');
    }
    
    // Failed routes details
    if (failed > 0) {
        log('\n❌ FAILED ROUTES:', 'red');
        results.filter(r => !r.passed).forEach(result => {
            log(`   ${result.method} ${result.route} - Status: ${result.status} (Expected: ${result.expected})`, 'red');
        });
    }
    
    log('\n✅ WORKING ROUTES:', 'green');
    results.filter(r => r.passed).forEach(result => {
        log(`   ${result.method} ${result.route} - Status: ${result.status}`, 'green');
    });
    
    log('\n🔗 Quick Links:', 'cyan');
    log(`   Root: ${BACKEND_URL}/`, 'blue');
    log(`   Health: ${BACKEND_URL}/health`, 'blue');
    log(`   API Docs: ${BACKEND_URL}/api`, 'blue');
    log(`   Admin: ${BACKEND_URL}/dashboard`, 'blue');
}

// Run the tests
if (require.main === module) {
    runTests().catch(error => {
        log(`\n💥 Test runner error: ${error.message}`, 'red');
        process.exit(1);
    });
}

module.exports = { runTests, testRoute };
