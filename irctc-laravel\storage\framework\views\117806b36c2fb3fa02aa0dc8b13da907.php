<?php $__env->startSection('title', 'Users Management'); ?>
<?php $__env->startSection('page-title', 'Users Management'); ?>

<?php $__env->startSection('content'); ?>
<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <form method="GET" action="<?php echo e(route('admin.users')); ?>" class="row g-3">
                    <div class="col-md-4">
                        <label for="search" class="form-label">Search Users</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="<?php echo e(request('search')); ?>" placeholder="Search by name or email">
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Status</option>
                            <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>Active</option>
                            <option value="suspended" <?php echo e(request('status') === 'suspended' ? 'selected' : ''); ?>>Suspended</option>
                            <option value="deleted" <?php echo e(request('status') === 'deleted' ? 'selected' : ''); ?>>Deleted</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid gap-2 d-md-flex">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Filter
                            </button>
                            <a href="<?php echo e(route('admin.users')); ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Clear
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-users me-2"></i>Users (<?php echo e($users->total()); ?>)</h5>
                <div class="d-flex gap-2">
                    <button class="btn btn-sm btn-success" onclick="exportUsers()">
                        <i class="fas fa-download me-2"></i>Export
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if($users->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Credits</th>
                                    <th>Bookings</th>
                                    <th>Transactions</th>
                                    <th>Status</th>
                                    <th>Joined</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php if($user->picture): ?>
                                                    <img src="<?php echo e($user->picture); ?>" alt="Profile" class="rounded-circle me-3" width="40" height="40">
                                                <?php else: ?>
                                                    <div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                                        <i class="fas fa-user"></i>
                                                    </div>
                                                <?php endif; ?>
                                                <div>
                                                    <div class="fw-bold"><?php echo e($user->name); ?></div>
                                                    <small class="text-muted"><?php echo e($user->email); ?></small>
                                                    <?php if($user->google_id): ?>
                                                        <span class="badge bg-danger ms-1" title="Google Login">G</span>
                                                    <?php endif; ?>
                                                    <?php if($user->facebook_id): ?>
                                                        <span class="badge bg-primary ms-1" title="Facebook Login">F</span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary fs-6"><?php echo e($user->available_tickets); ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo e($user->tickets_count); ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-success"><?php echo e($user->transactions_count); ?></span>
                                        </td>
                                        <td>
                                            <?php switch($user->status):
                                                case ('active'): ?>
                                                    <span class="badge bg-success">Active</span>
                                                    <?php break; ?>
                                                <?php case ('suspended'): ?>
                                                    <span class="badge bg-warning">Suspended</span>
                                                    <?php break; ?>
                                                <?php case ('deleted'): ?>
                                                    <span class="badge bg-danger">Deleted</span>
                                                    <?php break; ?>
                                                <?php default: ?>
                                                    <span class="badge bg-secondary"><?php echo e(ucfirst($user->status)); ?></span>
                                            <?php endswitch; ?>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?php echo e($user->created_at->format('M d, Y')); ?></small>
                                            <br>
                                            <small class="text-muted"><?php echo e($user->created_at->diffForHumans()); ?></small>
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    Actions
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="<?php echo e(route('admin.users.details', $user)); ?>">
                                                            <i class="fas fa-eye me-2"></i>View Details
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <button class="dropdown-item" onclick="addCredits(<?php echo e($user->id); ?>, '<?php echo e($user->name); ?>')">
                                                            <i class="fas fa-plus me-2"></i>Add Credits
                                                        </button>
                                                    </li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <?php if($user->status === 'active'): ?>
                                                        <li>
                                                            <button class="dropdown-item text-warning" onclick="updateStatus(<?php echo e($user->id); ?>, 'suspended')">
                                                                <i class="fas fa-pause me-2"></i>Suspend
                                                            </button>
                                                        </li>
                                                    <?php elseif($user->status === 'suspended'): ?>
                                                        <li>
                                                            <button class="dropdown-item text-success" onclick="updateStatus(<?php echo e($user->id); ?>, 'active')">
                                                                <i class="fas fa-play me-2"></i>Activate
                                                            </button>
                                                        </li>
                                                    <?php endif; ?>
                                                    <li>
                                                        <button class="dropdown-item text-danger" onclick="updateStatus(<?php echo e($user->id); ?>, 'deleted')">
                                                            <i class="fas fa-trash me-2"></i>Delete
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="card-footer bg-white">
                        <?php echo e($users->links()); ?>

                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No users found</h5>
                        <p class="text-muted">No users match your current filters.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Add Credits Modal -->
<div class="modal fade" id="addCreditsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Credits</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addCreditsForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="credits" class="form-label">Number of Credits</label>
                        <input type="number" class="form-control" id="credits" name="credits" min="1" max="1000" required>
                    </div>
                    <div class="mb-3">
                        <label for="reason" class="form-label">Reason</label>
                        <textarea class="form-control" id="reason" name="reason" rows="3" required placeholder="Enter reason for adding credits"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Credits</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Update Status Modal -->
<div class="modal fade" id="updateStatusModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Update User Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to update this user's status?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    This action will affect the user's access to the service.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" id="confirmStatusUpdate">Update Status</button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
let currentUserId = null;
let currentStatus = null;

function addCredits(userId, userName) {
    currentUserId = userId;
    document.getElementById('addCreditsModal').querySelector('.modal-title').textContent = `Add Credits to ${userName}`;
    const modal = new bootstrap.Modal(document.getElementById('addCreditsModal'));
    modal.show();
}

function updateStatus(userId, status) {
    currentUserId = userId;
    currentStatus = status;
    const modal = new bootstrap.Modal(document.getElementById('updateStatusModal'));
    modal.show();
}

function exportUsers() {
    // Implement export functionality
    alert('Export functionality will be implemented soon.');
}

// Handle add credits form submission
document.getElementById('addCreditsForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    
    submitBtn.disabled = true;
    submitBtn.textContent = 'Adding...';
    
    try {
        const response = await fetch(`<?php echo e(route('admin.users.add-credits', '')); ?>/${currentUserId}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: formData
        });
        
        if (response.ok) {
            location.reload();
        } else {
            const data = await response.json();
            alert('Error: ' + (data.message || 'Failed to add credits'));
        }
    } catch (error) {
        alert('Error: ' + error.message);
    } finally {
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
    }
});

// Handle status update confirmation
document.getElementById('confirmStatusUpdate').addEventListener('click', async function() {
    const submitBtn = this;
    const originalText = submitBtn.textContent;
    
    submitBtn.disabled = true;
    submitBtn.textContent = 'Updating...';
    
    try {
        const response = await fetch(`<?php echo e(route('admin.users.update-status', '')); ?>/${currentUserId}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ status: currentStatus })
        });
        
        if (response.ok) {
            location.reload();
        } else {
            const data = await response.json();
            alert('Error: ' + (data.message || 'Failed to update status'));
        }
    } catch (error) {
        alert('Error: ' + error.message);
    } finally {
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\chrome-ex\irctc-laravel\resources\views/admin/users.blade.php ENDPATH**/ ?>