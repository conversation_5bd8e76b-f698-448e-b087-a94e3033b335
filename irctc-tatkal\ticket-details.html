<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ticket Details - IRCTC Auto Booking</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="styles/ticket-details.css">
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-train"></i> IRCTC Auto Booking</h1>
            <div class="user-info" id="user-info">
                <img id="user-avatar" class="user-avatar" src="" alt="User Avatar">
                <div>
                    <span id="user-name">Loading...</span>
                    <span id="credits-badge"><i class="fas fa-ticket-alt"></i> <span id="credits-count">0</span> credits</span>
                </div>
            </div>
        </header>

        <main>
            <div class="ticket-details-card">
                <div class="card-header">
                    <h2>Ticket Details</h2>
                    <div class="ticket-status" id="ticket-status">Booked</div>
                </div>

                <div class="loading-container" id="loading-container">
                    <div class="loading-spinner"></div>
                    <p>Loading ticket details...</p>
                </div>

                <div class="ticket-content" id="ticket-content">
                    <!-- Journey Details Section -->
                    <div class="details-section">
                        <h3><i class="fas fa-route"></i> Journey Details</h3>
                        <div class="journey-header">
                            <div class="station-info">
                                <div class="station-name" id="from-station">New Delhi</div>
                                <div class="station-code" id="from-code">NDLS</div>
                            </div>
                            <div class="journey-arrow">
                                <i class="fas fa-long-arrow-alt-right"></i>
                            </div>
                            <div class="station-info">
                                <div class="station-name" id="to-station">Mumbai Central</div>
                                <div class="station-code" id="to-code">BCT</div>
                            </div>
                        </div>
                        
                        <div class="journey-details">
                            <div class="detail-item">
                                <span class="detail-label">Train Number:</span>
                                <span class="detail-value" id="train-number">12952</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Train Name:</span>
                                <span class="detail-value" id="train-name">Rajdhani Express</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Journey Date:</span>
                                <span class="detail-value" id="journey-date">15 May 2023</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Class:</span>
                                <span class="detail-value" id="journey-class">3A</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Quota:</span>
                                <span class="detail-value" id="journey-quota">Tatkal</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Boarding:</span>
                                <span class="detail-value" id="boarding-station">New Delhi (NDLS)</span>
                            </div>
                        </div>
                    </div>

                    <!-- Passenger Details Section -->
                    <div class="details-section">
                        <h3><i class="fas fa-users"></i> Passenger Details</h3>
                        <div class="passenger-table-container">
                            <table class="passenger-table">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Name</th>
                                        <th>Age</th>
                                        <th>Gender</th>
                                        <th>Berth</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody id="passengers-container">
                                    <!-- Passenger rows will be added here dynamically -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Infant Details Section (if any) -->
                    <div class="details-section" id="infant-section">
                        <h3><i class="fas fa-baby"></i> Infant Details</h3>
                        <div class="infant-table-container">
                            <table class="infant-table">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Name</th>
                                        <th>Age</th>
                                        <th>Gender</th>
                                    </tr>
                                </thead>
                                <tbody id="infants-container">
                                    <!-- Infant rows will be added here dynamically -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Booking Details Section -->
                    <div class="details-section">
                        <h3><i class="fas fa-info-circle"></i> Booking Details</h3>
                        <div class="booking-details">
                            <div class="detail-item">
                                <span class="detail-label">PNR Number:</span>
                                <span class="detail-value" id="pnr-number">2220553788</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Booking Date:</span>
                                <span class="detail-value" id="booking-date">12 May 2023, 10:15 AM</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Booking Status:</span>
                                <span class="detail-value" id="booking-status">Confirmed</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Mobile Number:</span>
                                <span class="detail-value" id="mobile-number">98XXXXXXXX</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Payment Method:</span>
                                <span class="detail-value" id="payment-method">UPI</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Transaction ID:</span>
                                <span class="detail-value" id="transaction-id">IRCTC12345678</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="ticket-actions">
                    <button id="btn-back" class="btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </button>
                    <button id="btn-print" class="btn-primary">
                        <i class="fas fa-print"></i> Print Ticket
                    </button>
                    <button id="btn-cancel" class="btn-danger">
                        <i class="fas fa-times"></i> Cancel Ticket
                    </button>
                </div>
            </div>
        </main>
        
        <footer>
            <div class="footer-links">
                <a href="dashboard.html"><i class="fas fa-home"></i> Dashboard</a>
                <a href="add-credits.html"><i class="fas fa-plus-circle"></i> Add Credits</a>
                <a href="#" id="help-link"><i class="fas fa-question-circle"></i> Help</a>
                <a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
            <div class="copyright">
                &copy; 2023 IRCTC Auto Booking Extension
            </div>
        </footer>
    </div>
    
    <script src="ticket-details.js"></script>
</body>
</html>
