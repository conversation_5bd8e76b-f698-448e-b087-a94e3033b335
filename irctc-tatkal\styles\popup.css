/* Base styles */
html {
    min-width: 360px !important;
    max-width: 360px !important;
    height: 500px !important;
    position: relative;
    background: #f5f5f5;
}

body {
    margin: 0;
    padding: 12px;
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    width: 360px !important;
    height: 500px !important;
    overflow: hidden;
}

.container {
    width: 100%;
    height: 100%;
    background: white;
    padding: 16px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Logo styles */
.logo {
    text-align: center;
    margin-bottom: 25px;
}

.logo img {
    width: 120px;
    height: auto;
}

/* Welcome text styles */
.welcome-text {
    text-align: center;
    margin-bottom: 30px;
}

.welcome-text h1 {
    font-size: 24px;
    color: #333;
    margin-bottom: 10px;
}

.welcome-text p {
    font-size: 14px;
    color: #666;
    margin: 0;
}

/* Login section styles */
.login-section {
    text-align: center;
    margin-bottom: 20px;
}

.login-title {
    font-size: 16px;
    color: #666;
    margin-bottom: 15px;
}

.social-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
}

.social-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #ddd;
    cursor: pointer;
    transition: all 0.2s;
    background: white;
}

.social-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.social-btn i {
    font-size: 20px;
}

/* Social button specific styles */
.social-btn.google i { 
    color: #4285f4; 
}

.social-btn.facebook { 
    background: #1877f2; 
    border: none;
}

.social-btn.facebook i { 
    color: white; 
}

.social-btn.microsoft i { 
    color: #00a4ef; 
}

.social-btn.apple i { 
    color: #000; 
}

/* Divider styles */
.divider {
    display: flex;
    align-items: center;
    text-align: center;
    margin: 20px 0;
    color: #666;
}

.divider::before,
.divider::after {
    content: '';
    flex: 1;
    border-bottom: 1px solid #ddd;
}

.divider span {
    padding: 0 10px;
    font-size: 12px;
}

/* Footer styles */
.footer {
    margin-top: 30px;
    text-align: center;
    font-size: 12px;
    color: #666;
}

.privacy-links {
    margin-top: 10px;
    display: flex;
    justify-content: center;
    gap: 20px;
}

.footer a {
    color: #4285f4;
    text-decoration: none;
}

.footer a:hover {
    text-decoration: underline;
}

/* Disabled state */
.social-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* Loading state */
.social-btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.social-btn.loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}