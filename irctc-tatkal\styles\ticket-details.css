:root {
    --primary-color: #4285f4;
    --primary-dark: #3367d6;
    --secondary-color: #34a853;
    --danger-color: #ea4335;
    --warning-color: #fbbc05;
    --text-color: #333;
    --dark-gray: #666;
    --medium-gray: #ccc;
    --light-gray: #f5f5f5;
    --border-radius: 4px;
    --box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: var(--text-color);
    line-height: 1.6;
    margin: 0;
    padding: 0;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

/* Header styles */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--medium-gray);
}

header h1 {
    font-size: 24px;
    color: var(--primary-color);
}

header h1 i {
    margin-right: 10px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.user-info div {
    display: flex;
    flex-direction: column;
}

#user-name {
    font-weight: 600;
}

#credits-badge {
    font-size: 14px;
    color: var(--secondary-color);
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Main content */
main {
    margin-bottom: 30px;
}

.ticket-details-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
    position: relative;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--medium-gray);
}

.card-header h2 {
    color: var(--primary-color);
    margin: 0;
}

.ticket-status {
    font-size: 14px;
    padding: 6px 12px;
    border-radius: 20px;
    text-transform: uppercase;
    font-weight: bold;
}

.ticket-status.booked {
    background: #e3f2fd;
    color: #1976d2;
}

.ticket-status.confirmed {
    background: #e8f5e9;
    color: #2e7d32;
}

.ticket-status.cancelled {
    background: #ffebee;
    color: #c62828;
}

.ticket-status.completed {
    background: #f5f5f5;
    color: #616161;
}

/* Loading container */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
}

.loading-spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top: 4px solid var(--primary-color);
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Ticket content */
.ticket-content {
    display: none; /* Will be shown when loaded */
}

.details-section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--light-gray);
}

.details-section:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.details-section h3 {
    font-size: 18px;
    color: var(--text-color);
    margin-top: 0;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.details-section h3 i {
    color: var(--primary-color);
}

/* Journey header */
.journey-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 15px;
    background-color: var(--light-gray);
    border-radius: var(--border-radius);
}

.station-info {
    text-align: center;
    flex: 1;
}

.station-name {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

.station-code {
    font-size: 14px;
    color: var(--dark-gray);
}

.journey-arrow {
    font-size: 24px;
    color: var(--primary-color);
    margin: 0 20px;
}

/* Journey details */
.journey-details, .booking-details {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.detail-item {
    display: flex;
    flex-direction: column;
}

.detail-label {
    font-size: 14px;
    color: var(--dark-gray);
    margin-bottom: 5px;
}

.detail-value {
    font-size: 16px;
    font-weight: 500;
}

/* Passenger and Infant tables */
.passenger-table-container, .infant-table-container {
    margin-bottom: 10px;
    overflow-x: auto;
}

.passenger-table, .infant-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.passenger-table th, .infant-table th {
    background-color: var(--light-gray);
    padding: 10px;
    text-align: left;
    font-weight: 600;
    border-bottom: 1px solid var(--medium-gray);
}

.passenger-table td, .infant-table td {
    padding: 10px;
    border-bottom: 1px solid var(--light-gray);
}

.passenger-status {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 12px;
    display: inline-block;
}

.passenger-status.confirmed {
    background: #e8f5e9;
    color: #2e7d32;
}

.passenger-status.rac {
    background: #fff3e0;
    color: #e65100;
}

.passenger-status.waitlisted {
    background: #ffebee;
    color: #c62828;
}

/* Ticket actions */
.ticket-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--light-gray);
}

.btn-primary, .btn-secondary, .btn-danger {
    padding: 10px 15px;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
    font-size: 14px;
    border: none;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-secondary {
    background-color: var(--light-gray);
    color: var(--text-color);
}

.btn-secondary:hover {
    background-color: var(--medium-gray);
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background-color: #d32f2f;
}

/* Footer styles */
footer {
    padding-top: 20px;
    border-top: 1px solid var(--medium-gray);
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.footer-links {
    display: flex;
    gap: 20px;
}

.footer-links a {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.footer-links a:hover {
    text-decoration: underline;
}

.copyright {
    font-size: 12px;
    color: var(--dark-gray);
}

/* Responsive styles */
@media (max-width: 768px) {
    .journey-details, .booking-details {
        grid-template-columns: 1fr;
    }
    
    .journey-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .journey-arrow {
        transform: rotate(90deg);
    }
    
    header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .ticket-actions {
        flex-direction: column;
    }
    
    .btn-primary, .btn-secondary, .btn-danger {
        width: 100%;
    }
}
