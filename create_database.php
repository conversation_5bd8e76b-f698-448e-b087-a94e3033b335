<?php
// Create MySQL database for Laravel project

$host = '127.0.0.1';
$port = '3306';
$username = 'root';
$password = '';
$database = 'irctc_laravel';

try {
    // Connect to MySQL server (without specifying database)
    $pdo = new PDO("mysql:host=$host;port=$port", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connected to MySQL server successfully!\n";
    
    // Create database if it doesn't exist
    $sql = "CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
    $pdo->exec($sql);
    
    echo "✅ Database '$database' created successfully!\n";
    
    // Test connection to the new database
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$database", $username, $password);
    echo "✅ Connected to database '$database' successfully!\n";
    
    echo "\n🎉 Database setup complete! You can now run Laravel migrations.\n";
    echo "Run: cd irctc-laravel && php artisan migrate:fresh --seed\n";
    
} catch (PDOException $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "\n💡 Make sure WAMP is running and MySQL service is started.\n";
    echo "💡 Check if the MySQL port (3306) is correct.\n";
    echo "💡 Verify the MySQL username and password.\n";
}
?>
