document.addEventListener('DOMContentLoaded', async function() {
    try {
        // Check if user is logged in
        const result = await new Promise(resolve => {
            chrome.storage.local.get(['userInfo', 'authToken'], resolve);
        });

        console.log('Auth token exists:', !!result.authToken);

        if (!result.userInfo || !result.authToken) {
            console.log('No user info or auth token found, redirecting to login');
            window.location.href = 'popup.html';
            return;
        }

        // Initialize the booking form
        initializeBookingForm(result.userInfo, result.authToken);

    } catch (error) {
        console.error('Initialization error:', error);
        showError('Failed to initialize booking form. Please try again.');
    }
});

// Initialize the booking form
async function initializeBookingForm(userInfo, authToken) {
    try {
        // Update user info in the UI
        document.getElementById('user-name').textContent = userInfo.name;
        document.getElementById('user-avatar').src = userInfo.picture || 'images/default-avatar.png';

        // Fetch available booking credits
        await fetchCreditsCount(authToken);

        // Fetch recent bookings
        await fetchRecentBookings(authToken);

        // Load station list
        await loadStationList();

        // Set up event listeners
        setupEventListeners(authToken);

        // Set minimum date for journey date to today
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('journey-date').min = today;

        // Load draft if exists
        loadDraft();

    } catch (error) {
        console.error('Form initialization error:', error);
        showError('Failed to initialize form. Please try again.');
    }
}

// Load station list from JSON file
async function loadStationList() {
    try {
        const response = await fetch('stationlist.json');
        if (!response.ok) {
            throw new Error('Failed to load station list');
        }

        const stations = await response.json();
        const datalist = document.getElementById('stations-list');

        // Clear existing options
        datalist.innerHTML = '';

        // Add stations to datalist
        stations.forEach(station => {
            const option = document.createElement('option');
            option.value = `${station.name} - ${station.code}`;
            datalist.appendChild(option);
        });

        console.log(`Loaded ${stations.length} stations`);
    } catch (error) {
        console.error('Error loading station list:', error);
    }
}

// Set up all event listeners
function setupEventListeners(authToken) {

    // Form submission
    document.getElementById('booking-form').addEventListener('submit', async function(e) {
        e.preventDefault();
        await handleFormSubmission(authToken);
    });

    // Add passenger button
    document.getElementById('add-passenger').addEventListener('click', function() {
        addPassenger();
    });

    // Add infant button
    document.getElementById('add-infant').addEventListener('click', function() {
        addInfant();
    });

    // Save draft button
    document.getElementById('save-draft').addEventListener('click', function() {
        saveDraft();
    });

    // Save ticket button
    document.getElementById('save-ticket').addEventListener('click', function() {
        saveTicket();
    });

    // Clear form button
    document.getElementById('clear-form').addEventListener('click', function() {
        if (confirm('Are you sure you want to clear all form data? This cannot be undone.')) {
            clearForm();
        }
    });

    // Buy more credits button is now a direct link to add-credits.html

    // Station input suggestions
    setupStationSuggestions('from-station', 'from-suggestions');
    setupStationSuggestions('to-station', 'to-suggestions');
    setupStationSuggestions('boarding-station', 'boarding-suggestions');

    // Payment method selection
    document.querySelectorAll('input[name="payment-method"]').forEach(radio => {
        radio.addEventListener('change', function() {
            updatePaymentMethodDetails(this.value);
        });
    });

    // Bank selection for HDFC password field
    document.getElementById('bank-name').addEventListener('change', function() {
        const hdfcPasswordRow = document.getElementById('hdfc-password-row');
        if (this.value === 'HDFC') {
            hdfcPasswordRow.classList.remove('hidden');
        } else {
            hdfcPasswordRow.classList.add('hidden');
        }
    });

    // Logout button
    document.getElementById('logout-btn').addEventListener('click', async () => {
        try {
            const token = await new Promise((resolve, reject) => {
                chrome.identity.getAuthToken({ interactive: false }, (token) => {
                    if (chrome.runtime.lastError) {
                        reject(chrome.runtime.lastError);
                        return;
                    }
                    resolve(token);
                });
            });

            if (token) {
                await new Promise((resolve, reject) => {
                    chrome.identity.removeCachedAuthToken({ token }, () => {
                        if (chrome.runtime.lastError) {
                            reject(chrome.runtime.lastError);
                            return;
                        }
                        resolve();
                    });
                });

                await fetch(`https://accounts.google.com/o/oauth2/revoke?token=${token}`);
            }

            await new Promise((resolve) => {
                chrome.storage.local.clear(() => {
                    resolve();
                });
            });

            window.location.href = 'popup.html';
        } catch (error) {
            console.error('Logout failed:', error);
            alert('Logout failed: ' + error.message);
        }
    });

    // Help link
    document.getElementById('help-link').addEventListener('click', function(e) {
        e.preventDefault();
        chrome.tabs.create({
            url: 'http://localhost:3000/help'
        });
    });
}



// Update payment method details based on selection
function updatePaymentMethodDetails(method) {
    // Hide all payment detail sections
    document.querySelectorAll('.payment-detail-section').forEach(section => {
        section.classList.add('hidden');
    });

    // Show the selected payment method details
    const selectedSection = document.getElementById(`${method}-details`);
    if (selectedSection) {
        selectedSection.classList.remove('hidden');
    }
}

// Handle form submission
async function handleFormSubmission(authToken) {
    try {
        // Show loading state
        const bookButton = document.getElementById('book-now');
        const originalText = bookButton.innerHTML;
        bookButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        bookButton.disabled = true;

        // Check if user has available booking credits
        const creditsCount = await fetchCreditsCount(authToken);
        if (creditsCount <= 0) {
            if (confirm('You have no booking credits available. Would you like to purchase credits now?')) {
                window.location.href = 'add-credits.html';
                return;
            }
            bookButton.innerHTML = originalText;
            bookButton.disabled = false;
            return;
        }

        // Get form data
        const formData = getFormData();
        console.log('Form data:', formData);

        // Validate form data
        if (!validateFormData(formData)) {
            bookButton.innerHTML = originalText;
            bookButton.disabled = false;
            return;
        }

        // Save as draft
        saveDraft();

        // Store the booking data in chrome.storage for the content script to use
        await new Promise((resolve) => {
            chrome.storage.local.set({
                'irctc_credentials': {
                    user_name: formData.irctcUsername,
                    password: formData.irctcPassword
                },
                'journey_details': {
                    from: formData.fromStation,
                    destination: formData.toStation,
                    date: formData.journeyDate,
                    class: formData.class,
                    quota: formData.quota,
                    'train-no': formData.trainNumber || '00000',
                    boarding: formData.boardingStation || ''
                },
                'passenger_details': formData.passengers.map(passenger => ({
                    name: passenger.name,
                    age: passenger.age,
                    gender: passenger.gender,
                    berth: passenger.berthPreference || '',
                    food: passenger.foodChoice || '',
                    passengerchildberth: false
                })),
                'infant_details': formData.infants.map(infant => ({
                    name: infant.name,
                    gender: infant.gender,
                    age: infant.belowOneYear ? 'Below 1 Year' : '1 Year'
                })),
                'other_preferences': {
                    mobileNumber: formData.mobileNumber,
                    autoCaptcha: true,
                    autoUpgradation: formData.autoUpgrade,
                    confirmberths: formData.bookConfirmBerth,
                    paymentmethod: getPaymentMethodCode(formData.paymentMethod),
                    acbooktime: formData.customTime || '09:59:59',
                    slbooktime: formData.customTime || '10:59:59',
                    gnbooktime: formData.customTime || '07:59:59',
                    CaptchaSubmitMode: formData.submitMode === 'auto' ? 'A' : 'M'
                },
                'travel_preferences': {
                    AvailabilityCheck: 'A',
                    travelInsuranceOpted: formData.travelInsurance ? 'yes' : 'no',
                    prefcoach: formData.coachNumber || '',
                    reservationchoice: 'LOWER'
                }
            }, resolve);
        });

        // Book the ticket through our API to track usage
        try {
            const response = await fetch(getApiUrl('/tickets/book'), {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });

            const data = await response.json();

            if (!response.ok) {
                console.warn('API booking notification failed, but continuing with IRCTC booking:', data.message);
            }
        } catch (apiError) {
            console.warn('API booking notification failed, but continuing with IRCTC booking:', apiError);
        }

        // Open IRCTC website in a new tab
        chrome.tabs.create({
            url: 'https://www.irctc.co.in/nget/train-search'
        });

        // Show success message
        alert('Booking process initiated. Please wait while we process your request on the IRCTC website.');

        // Reset button state
        bookButton.innerHTML = originalText;
        bookButton.disabled = false;

    } catch (error) {
        console.error('Booking error:', error);
        alert('Booking failed: ' + error.message);

        // Reset button state
        const bookButton = document.getElementById('book-now');
        bookButton.innerHTML = '<i class="fas fa-ticket-alt"></i> Book Now';
        bookButton.disabled = false;
    }
}

// Helper function to convert payment method to code
function getPaymentMethodCode(method) {
    switch (method) {
        case 'irctc-qr':
            return 'IRCUPI';
        case 'upi':
            return 'PAYTMUPI';
        case 'card':
            return 'HDFCDB';
        case 'netbanking':
            return 'HDFCDB';
        default:
            return 'IRCUPI';
    }
}

// Get form data
function getFormData() {
    const formData = {
        // IRCTC Login Details
        irctcUsername: document.getElementById('irctc-username').value,
        irctcPassword: document.getElementById('irctc-password').value,

        // Journey Details
        fromStation: document.getElementById('from-station').value,
        toStation: document.getElementById('to-station').value,
        journeyDate: document.getElementById('journey-date').value,
        trainNumber: document.getElementById('train-number').value || null,
        class: document.getElementById('class').value,
        quota: document.getElementById('quota').value,
        boardingStation: document.getElementById('boarding-station').value || null,
        mobileNumber: document.getElementById('mobile-number').value,

        // Auto-Click Time Settings
        customTime: document.getElementById('custom-time').value || null,

        // Additional Choices
        captchaAutofill: document.getElementById('captcha-autofill').checked,
        submitMode: document.getElementById('submit-mode').value,
        autoUpgrade: document.getElementById('auto-upgrade').checked,
        bookConfirmBerth: document.getElementById('book-confirm-berth').checked,
        travelInsurance: document.getElementById('travel-insurance').checked,

        // Reservation Preferences
        coachNumber: document.getElementById('coach-number').value || null,

        // Payment Details
        paymentMethod: document.querySelector('input[name="payment-method"]:checked').value,

        // Passengers and Infants
        passengers: [],
        infants: []
    };

    // Add payment method specific details
    if (formData.paymentMethod === 'upi') {
        formData.upiId = document.getElementById('upi-id').value || null;
    } else if (formData.paymentMethod === 'card') {
        formData.cardDetails = {
            number: document.getElementById('card-number').value || null,
            expiry: document.getElementById('card-expiry').value || null,
            cvv: document.getElementById('card-cvv').value || null,
            name: document.getElementById('card-name').value || null
        };
    } else if (formData.paymentMethod === 'netbanking') {
        formData.bankName = document.getElementById('bank-name').value || null;
        if (formData.bankName === 'HDFC') {
            formData.hdfcPassword = document.getElementById('hdfc-password').value || null;
        }
    }

    // Get passenger data
    const passengerRows = document.querySelectorAll('.passenger-row');
    passengerRows.forEach(row => {
        const passengerIndex = row.dataset.passengerIndex;
        const passenger = {
            name: document.getElementById(`passenger-name-${passengerIndex}`).value,
            age: document.getElementById(`passenger-age-${passengerIndex}`).value,
            gender: document.getElementById(`passenger-gender-${passengerIndex}`).value,
            berthPreference: document.getElementById(`passenger-berth-${passengerIndex}`).value || null,
            foodChoice: document.getElementById(`passenger-food-${passengerIndex}`).value || null
        };
        formData.passengers.push(passenger);
    });

    // Get infant data
    const infantRows = document.querySelectorAll('.infant-row');
    infantRows.forEach(row => {
        const infantIndex = row.dataset.infantIndex;
        const infant = {
            name: document.getElementById(`infant-name-${infantIndex}`).value,
            gender: document.getElementById(`infant-gender-${infantIndex}`).value,
            belowOneYear: document.getElementById(`infant-below-one-${infantIndex}`).checked
        };
        formData.infants.push(infant);
    });

    return formData;
}

// Validate form data
function validateFormData(formData) {
    // Basic validation
    if (!formData.fromStation) {
        alert('Please enter a from station');
        return false;
    }

    if (!formData.toStation) {
        alert('Please enter a to station');
        return false;
    }

    if (!formData.journeyDate) {
        alert('Please select a journey date');
        return false;
    }

    if (!formData.class) {
        alert('Please select a travel class');
        return false;
    }

    if (formData.passengers.length === 0) {
        alert('Please add at least one passenger');
        return false;
    }

    // Validate each passenger
    for (let i = 0; i < formData.passengers.length; i++) {
        const passenger = formData.passengers[i];
        if (!passenger.name) {
            alert(`Please enter a name for passenger ${i + 1}`);
            return false;
        }

        if (!passenger.age) {
            alert(`Please enter an age for passenger ${i + 1}`);
            return false;
        }

        if (!passenger.gender) {
            alert(`Please select a gender for passenger ${i + 1}`);
            return false;
        }
    }

    return true;
}

// Add a new passenger
function addPassenger() {
    const passengersContainer = document.getElementById('passengers-container');
    const passengerRows = document.querySelectorAll('.passenger-row');

    // Enable remove button for the first passenger if adding a second one
    if (passengerRows.length === 1) {
        passengerRows[0].querySelector('.remove-passenger').disabled = false;
    }

    // Maximum 6 passengers
    if (passengerRows.length >= 6) {
        alert('Maximum 6 passengers allowed');
        return;
    }

    const newIndex = passengerRows.length;
    const passengerRow = document.createElement('tr');
    passengerRow.className = 'passenger-row';
    passengerRow.dataset.passengerIndex = newIndex;

    passengerRow.innerHTML = `
        <td>${newIndex + 1}</td>
        <td><input type="text" id="passenger-name-${newIndex}" name="passenger-name-${newIndex}" placeholder="Full name" required></td>
        <td><input type="number" id="passenger-age-${newIndex}" name="passenger-age-${newIndex}" min="1" max="120" placeholder="Age" required style="width: 50px;"></td>
        <td>
            <select id="passenger-gender-${newIndex}" name="passenger-gender-${newIndex}" required>
                <option value="">Select</option>
                <option value="M">Male</option>
                <option value="F">Female</option>
                <option value="O">Other</option>
            </select>
        </td>
        <td>
            <select id="passenger-berth-${newIndex}" name="passenger-berth-${newIndex}">
                <option value="">No Pref</option>
                <option value="LB">Lower</option>
                <option value="MB">Middle</option>
                <option value="UB">Upper</option>
                <option value="SL">Side L</option>
                <option value="SU">Side U</option>
            </select>
        </td>
        <td>
            <select id="passenger-food-${newIndex}" name="passenger-food-${newIndex}">
                <option value="">-</option>
                <option value="V">Veg</option>
                <option value="N">Non-Veg</option>
            </select>
        </td>
        <td>
            <button type="button" class="btn-icon remove-passenger"><i class="fas fa-times"></i></button>
        </td>
    `;

    passengersContainer.appendChild(passengerRow);

    // Add event listener for remove button
    passengerRow.querySelector('.remove-passenger').addEventListener('click', function() {
        removePassenger(newIndex);
    });
}

// Add a new infant
function addInfant() {
    const infantsContainer = document.getElementById('infants-container');
    const infantRows = document.querySelectorAll('.infant-row');

    // Maximum 2 infants
    if (infantRows.length >= 2) {
        alert('Maximum 2 infants allowed');
        return;
    }

    // Hide the no infants message
    const noInfantsMessage = document.querySelector('.no-infants-message');
    if (noInfantsMessage) {
        noInfantsMessage.style.display = 'none';
    }

    const newIndex = infantRows.length;
    const infantRow = document.createElement('tr');
    infantRow.className = 'infant-row';
    infantRow.dataset.infantIndex = newIndex;

    infantRow.innerHTML = `
        <td>${newIndex + 1}</td>
        <td><input type="text" id="infant-name-${newIndex}" name="infant-name-${newIndex}" placeholder="Infant name" required></td>
        <td>
            <select id="infant-gender-${newIndex}" name="infant-gender-${newIndex}" required>
                <option value="">Select</option>
                <option value="M">Male</option>
                <option value="F">Female</option>
            </select>
        </td>
        <td>
            <div class="toggle-switch">
                <input type="checkbox" id="infant-below-one-${newIndex}" name="infant-below-one-${newIndex}" checked>
                <label for="infant-below-one-${newIndex}"></label>
            </div>
        </td>
        <td>
            <button type="button" class="btn-icon remove-infant"><i class="fas fa-times"></i></button>
        </td>
    `;

    infantsContainer.appendChild(infantRow);

    // Add event listener for remove button
    infantRow.querySelector('.remove-infant').addEventListener('click', function() {
        removeInfant(newIndex);
    });
}

// Remove an infant
function removeInfant(index) {
    const infantsContainer = document.getElementById('infants-container');
    const infantRows = document.querySelectorAll('.infant-row');

    // Find the row with the matching index
    const rowToRemove = document.querySelector(`.infant-row[data-infant-index="${index}"]`);
    if (rowToRemove) {
        infantsContainer.removeChild(rowToRemove);
    }

    // If no infants left, show the no infants message
    if (infantRows.length <= 1) {
        document.querySelector('.no-infants-message').style.display = 'block';
    }

    // Update infant numbers for remaining rows
    const remainingRows = document.querySelectorAll('.infant-row');
    remainingRows.forEach((row, i) => {
        // Update the infant number in the first cell
        row.cells[0].textContent = i + 1;
    });
}

// Clear the form
function clearForm() {
    // Reset the form
    document.getElementById('booking-form').reset();

    // Clear passengers (keep only the first one)
    const passengersContainer = document.getElementById('passengers-container');
    const passengerRows = document.querySelectorAll('.passenger-row');

    // Remove all passengers except the first one
    for (let i = passengerRows.length - 1; i > 0; i--) {
        passengersContainer.removeChild(passengerRows[i]);
    }

    // Reset the first passenger's fields
    if (passengerRows.length > 0) {
        const firstPassenger = passengerRows[0];
        firstPassenger.querySelector('input[id^="passenger-name"]').value = '';
        firstPassenger.querySelector('input[id^="passenger-age"]').value = '';
        firstPassenger.querySelector('select[id^="passenger-gender"]').value = '';
        firstPassenger.querySelector('select[id^="passenger-berth"]').value = '';
        firstPassenger.querySelector('select[id^="passenger-food"]').value = '';

        // Disable the remove button for the first passenger
        firstPassenger.querySelector('.remove-passenger').disabled = true;
    }

    // Clear infants
    const infantsContainer = document.getElementById('infants-container');
    infantsContainer.innerHTML = '';
    document.querySelector('.no-infants-message').style.display = 'block';

    // Reset payment method to default (IRCTC QR Code)
    document.getElementById('payment-irctc-qr').checked = true;
    updatePaymentMethodDetails('irctc-qr');

    // Clear draft from storage
    clearDraft();

    // Show success message
    alert('Form cleared successfully');
}

// Remove a passenger
function removePassenger(index) {
    const passengersContainer = document.getElementById('passengers-container');
    const passengerRows = document.querySelectorAll('.passenger-row');

    // Don't remove if only one passenger left
    if (passengerRows.length <= 1) {
        return;
    }

    // Find the row with the matching index
    const rowToRemove = document.querySelector(`.passenger-row[data-passenger-index="${index}"]`);
    if (rowToRemove) {
        passengersContainer.removeChild(rowToRemove);
    }

    // Update passenger numbers and indices
    const remainingRows = document.querySelectorAll('.passenger-row');
    remainingRows.forEach((row, i) => {
        // Update the passenger number in the first cell
        row.cells[0].textContent = i + 1;

        // Disable remove button if only one passenger left
        if (remainingRows.length === 1) {
            row.querySelector('.remove-passenger').disabled = true;
        }
    });
}

// Save form data as draft
function saveDraft() {
    const formData = getFormData();
    console.log(formData);
    chrome.storage.local.set({ 'bookingDraft': formData }, function() {
        alert('Draft saved successfully');
    });
}

// Save ticket details for later use
function saveTicket() {
    try {
        // Get form data
        const formData = getFormData();

        // Validate form data
        if (!validateFormData(formData)) {
            return;
        }

        console.log('Saving ticket with form data:', formData);

        // Get existing saved tickets
        chrome.storage.local.get(['savedTickets'], function(result) {
            console.log('Current saved tickets:', result.savedTickets);
            const savedTickets = result.savedTickets || [];

            // Add timestamp to identify when the ticket was saved
            formData.savedAt = new Date().toISOString();

            // Add the new ticket to the array
            savedTickets.push(formData);

            console.log('Updated saved tickets array:', savedTickets);

            // Save the updated array back to storage
            chrome.storage.local.set({ 'savedTickets': savedTickets }, function() {
                if (chrome.runtime.lastError) {
                    console.error('Error saving to storage:', chrome.runtime.lastError);
                    alert('Failed to save ticket: ' + chrome.runtime.lastError.message);
                    return;
                }

                console.log('Ticket saved successfully to storage');
                alert('Ticket details saved successfully. You can access it from the dashboard.');

                // Verify the data was saved correctly
                chrome.storage.local.get(['savedTickets'], function(verifyResult) {
                    console.log('Verification - saved tickets in storage:', verifyResult.savedTickets);
                });
            });
        });
    } catch (error) {
        console.error('Error saving ticket:', error);
        alert('Failed to save ticket: ' + error.message);
    }
}

// Load draft data
function loadDraft() {
    chrome.storage.local.get(['bookingDraft'], function(result) {
        if (result.bookingDraft) {
            const draft = result.bookingDraft;

            // Fill basic journey details
            document.getElementById('from-station').value = draft.fromStation || '';
            document.getElementById('to-station').value = draft.toStation || '';
            document.getElementById('journey-date').value = draft.journeyDate || '';
            document.getElementById('class').value = draft.class || '';

            // Fill booking options
            document.getElementById('quota').value = draft.quota || 'GN';
            document.getElementById('auto-upgrade').checked = draft.autoUpgrade || false;
            document.getElementById('book-confirm-berth').checked = draft.bookConfirmBerth || false;
            document.getElementById('book-time').value = draft.bookTime || '';

            // Set payment method
            if (draft.paymentMethod) {
                document.querySelector(`input[name="payment-method"][value="${draft.paymentMethod}"]`).checked = true;
            }

            // Fill passenger details
            if (draft.passengers && draft.passengers.length > 0) {
                // Remove default passenger
                const passengersContainer = document.getElementById('passengers-container');
                passengersContainer.innerHTML = '';

                // Add each passenger from draft
                draft.passengers.forEach((passenger, index) => {
                    // Create passenger row
                    const passengerRow = document.createElement('tr');
                    passengerRow.className = 'passenger-row';
                    passengerRow.dataset.passengerIndex = index;

                    passengerRow.innerHTML = `
                        <td>${index + 1}</td>
                        <td><input type="text" id="passenger-name-${index}" name="passenger-name-${index}" placeholder="Full name" value="${passenger.name || ''}" required></td>
                        <td><input type="number" id="passenger-age-${index}" name="passenger-age-${index}" min="1" max="120" placeholder="Age" value="${passenger.age || ''}" required style="width: 50px;"></td>
                        <td>
                            <select id="passenger-gender-${index}" name="passenger-gender-${index}" required>
                                <option value="">Select</option>
                                <option value="M" ${passenger.gender === 'M' ? 'selected' : ''}>Male</option>
                                <option value="F" ${passenger.gender === 'F' ? 'selected' : ''}>Female</option>
                                <option value="O" ${passenger.gender === 'O' ? 'selected' : ''}>Other</option>
                            </select>
                        </td>
                        <td>
                            <select id="passenger-berth-${index}" name="passenger-berth-${index}">
                                <option value="">No Pref</option>
                                <option value="LB" ${passenger.berthPreference === 'LB' ? 'selected' : ''}>Lower</option>
                                <option value="MB" ${passenger.berthPreference === 'MB' ? 'selected' : ''}>Middle</option>
                                <option value="UB" ${passenger.berthPreference === 'UB' ? 'selected' : ''}>Upper</option>
                                <option value="SL" ${passenger.berthPreference === 'SL' ? 'selected' : ''}>Side L</option>
                                <option value="SU" ${passenger.berthPreference === 'SU' ? 'selected' : ''}>Side U</option>
                            </select>
                        </td>
                        <td>
                            <select id="passenger-food-${index}" name="passenger-food-${index}">
                                <option value="">-</option>
                                <option value="V" ${passenger.foodChoice === 'V' ? 'selected' : ''}>Veg</option>
                                <option value="N" ${passenger.foodChoice === 'N' ? 'selected' : ''}>Non-Veg</option>
                            </select>
                        </td>
                        <td>
                            <button type="button" class="btn-icon remove-passenger" ${index === 0 && draft.passengers.length === 1 ? 'disabled' : ''}><i class="fas fa-times"></i></button>
                        </td>
                    `;

                    passengersContainer.appendChild(passengerRow);

                    // Add event listener for remove button
                    passengerRow.querySelector('.remove-passenger').addEventListener('click', function() {
                        removePassenger(index);
                    });
                });
            }

            // Fill infant details
            if (draft.infants && draft.infants.length > 0) {
                // Hide the no infants message
                document.querySelector('.no-infants-message').style.display = 'none';

                // Add each infant from draft
                draft.infants.forEach((infant, index) => {
                    // Create infant row
                    const infantRow = document.createElement('tr');
                    infantRow.className = 'infant-row';
                    infantRow.dataset.infantIndex = index;

                    infantRow.innerHTML = `
                        <td>${index + 1}</td>
                        <td><input type="text" id="infant-name-${index}" name="infant-name-${index}" placeholder="Infant name" value="${infant.name || ''}" required></td>
                        <td>
                            <select id="infant-gender-${index}" name="infant-gender-${index}" required>
                                <option value="">Select</option>
                                <option value="M" ${infant.gender === 'M' ? 'selected' : ''}>Male</option>
                                <option value="F" ${infant.gender === 'F' ? 'selected' : ''}>Female</option>
                            </select>
                        </td>
                        <td>
                            <div class="toggle-switch">
                                <input type="checkbox" id="infant-below-one-${index}" name="infant-below-one-${index}" ${infant.belowOneYear ? 'checked' : ''}>
                                <label for="infant-below-one-${index}"></label>
                            </div>
                        </td>
                        <td>
                            <button type="button" class="btn-icon remove-infant"><i class="fas fa-times"></i></button>
                        </td>
                    `;

                    document.getElementById('infants-container').appendChild(infantRow);

                    // Add event listener for remove button
                    infantRow.querySelector('.remove-infant').addEventListener('click', function() {
                        removeInfant(index);
                    });
                });
            }

            console.log('Draft loaded successfully');
        }
    });
}

// Clear draft data
function clearDraft() {
    chrome.storage.local.remove('bookingDraft', function() {
        console.log('Draft cleared');
    });
}

// Fetch available booking credits count
async function fetchCreditsCount(authToken) {
    if (!authToken) {
        console.error('No auth token provided');
        return 0;
    }

    try {
        console.log('Fetching booking credits count...');
        const response = await fetch('http://localhost:3000/api/tickets/count', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });

        console.log('Response status:', response.status);
        const data = await response.json();
        console.log('Response data:', data);

        if (!response.ok) {
            // Check if token expired
            if (response.status === 401) {
                console.log('Token expired, redirecting to login');
                // Clear storage and redirect to login
                await chrome.storage.local.remove(['authToken', 'userInfo', 'isLoggedIn']);
                window.location.href = 'popup.html';
                return 0;
            }
            throw new Error(data.message || 'Failed to fetch booking credits count');
        }

        const count = data.count || 0;

        // Update UI
        document.getElementById('credits-count').textContent = count;
        document.getElementById('sidebar-credits-count').textContent = count;

        return count;

    } catch (error) {
        console.error('Error fetching booking credits count:', error);
        return 0;
    }
}

// Fetch recent bookings
async function fetchRecentBookings(authToken) {
    if (!authToken) {
        console.error('No auth token provided');
        return [];
    }

    const container = document.getElementById('recent-bookings');

    try {
        console.log('Fetching recent bookings...');
        const response = await fetch(getApiUrl('/tickets/booked'), {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });

        console.log('Response status:', response.status);
        const data = await response.json();
        console.log('Response data:', data);

        if (!response.ok) {
            // Check if token expired
            if (response.status === 401) {
                console.log('Token expired, redirecting to login');
                // Clear storage and redirect to login
                await chrome.storage.local.remove(['authToken', 'userInfo', 'isLoggedIn']);
                window.location.href = 'popup.html';
                return [];
            }
            throw new Error(data.message || 'Failed to fetch recent bookings');
        }

        const tickets = data.tickets || [];

        // Clear container
        container.innerHTML = '';

        if (tickets.length === 0) {
            container.innerHTML = '<p class="no-bookings">No recent bookings found</p>';
            return [];
        }

        // Show only the 3 most recent bookings
        const recentTickets = tickets.slice(0, 3);

        // Render recent bookings
        recentTickets.forEach(ticket => {
            const bookingItem = document.createElement('div');
            bookingItem.className = `booking-item ${ticket.status}`;

            // Format date
            const journeyDate = new Date(ticket.journeyDate).toLocaleDateString();

            bookingItem.innerHTML = `
                <div class="booking-route">${ticket.fromStation} → ${ticket.toStation}</div>
                <div class="booking-details">
                    <span class="booking-date">${journeyDate}</span>
                    <span class="booking-class">${ticket.class}</span>
                    <span class="booking-status ${ticket.status}">${ticket.status}</span>
                </div>
                <div class="booking-pnr">PNR: ${ticket.pnr}</div>
            `;

            container.appendChild(bookingItem);
        });

        return tickets;

    } catch (error) {
        console.error('Error fetching recent bookings:', error);
        container.innerHTML = `<p class="error-message">Failed to load bookings: ${error.message}</p>`;
        return [];
    }
}

// Set up station suggestions
function setupStationSuggestions(inputId, suggestionsId) {
    const input = document.getElementById(inputId);
    const suggestions = document.getElementById(suggestionsId);

    // Sample station data (in a real app, this would come from an API)
    const stations = [
        { code: 'NDLS', name: 'New Delhi' },
        { code: 'DLI', name: 'Delhi' },
        { code: 'MMCT', name: 'Mumbai Central' },
        { code: 'BCT', name: 'Mumbai Churchgate' },
        { code: 'CSTM', name: 'Mumbai CST' },
        { code: 'MAS', name: 'Chennai Central' },
        { code: 'SBC', name: 'Bengaluru' },
        { code: 'HWH', name: 'Howrah' },
        { code: 'PUNE', name: 'Pune' },
        { code: 'JAT', name: 'Jammu Tawi' }
    ];

    input.addEventListener('input', function() {
        const value = this.value.toLowerCase();

        if (value.length < 2) {
            suggestions.style.display = 'none';
            return;
        }

        // Filter stations based on input
        const filtered = stations.filter(station =>
            station.code.toLowerCase().includes(value) ||
            station.name.toLowerCase().includes(value)
        );

        // Clear previous suggestions
        suggestions.innerHTML = '';

        if (filtered.length === 0) {
            suggestions.style.display = 'none';
            return;
        }

        // Add new suggestions
        filtered.forEach(station => {
            const item = document.createElement('div');
            item.className = 'station-suggestion-item';
            item.textContent = `${station.code} - ${station.name}`;

            item.addEventListener('click', function() {
                input.value = station.code;
                suggestions.style.display = 'none';
            });

            suggestions.appendChild(item);
        });

        suggestions.style.display = 'block';
    });

    // Hide suggestions when clicking outside
    document.addEventListener('click', function(e) {
        if (e.target !== input && e.target !== suggestions) {
            suggestions.style.display = 'none';
        }
    });
}

// Show error message
function showError(message) {
    alert(message);
}
