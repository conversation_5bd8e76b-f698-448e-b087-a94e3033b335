<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Admin;
use App\Models\Ticket;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class AdminController extends Controller
{
    /**
     * Show admin login form
     */
    public function showLogin()
    {
        return view('admin.login');
    }

    /**
     * Handle admin login
     */
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        $admin = Admin::where('email', $request->email)->first();

        if ($admin && Hash::check($request->password, $admin->password) && $admin->isActive()) {
            Auth::guard('admin')->login($admin);
            $admin->update(['last_login' => now()]);

            return redirect()->route('admin.dashboard')->with('success', 'Welcome back!');
        }

        return back()->withErrors(['email' => 'Invalid credentials or account is inactive.']);
    }

    /**
     * Show admin dashboard
     */
    public function dashboard()
    {
        $stats = [
            'total_users' => User::count(),
            'active_users' => User::where('status', 'active')->count(),
            'total_tickets' => Ticket::count(),
            'total_revenue' => Transaction::where('status', 'completed')->sum('amount'),
            'pending_transactions' => Transaction::whereIn('status', ['created', 'pending'])->count(),
        ];

        $recentUsers = User::latest()->limit(5)->get();
        $recentTransactions = Transaction::with('user')->latest()->limit(5)->get();

        return view('admin.dashboard', compact('stats', 'recentUsers', 'recentTransactions'));
    }

    /**
     * Show users list
     */
    public function users(Request $request)
    {
        $query = User::query();

        // Search functionality
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by status
        if ($request->status) {
            $query->where('status', $request->status);
        }

        $users = $query->withCount(['tickets', 'transactions'])
                      ->orderBy('created_at', 'desc')
                      ->paginate(20);

        return view('admin.users', compact('users'));
    }

    /**
     * Show user details
     */
    public function userDetails(User $user)
    {
        $user->load(['tickets', 'transactions']);

        return view('admin.user-details', compact('user'));
    }

    /**
     * Add credits to user
     */
    public function addCredits(Request $request, User $user)
    {
        $request->validate([
            'credits' => 'required|integer|min:1|max:1000',
            'reason' => 'required|string|max:255',
        ]);

        $user->increment('available_tickets', $request->credits);

        // Create a transaction record
        Transaction::create([
            'user_id' => $user->id,
            'order_id' => 'ADMIN_' . time(),
            'amount' => 0,
            'currency' => 'INR',
            'credits' => $request->credits,
            'status' => 'completed',
            'type' => 'bonus',
            'description' => 'Admin added credits: ' . $request->reason,
        ]);

        return back()->with('success', "Added {$request->credits} credits to {$user->name}'s account.");
    }

    /**
     * Update user status
     */
    public function updateUserStatus(Request $request, User $user)
    {
        $request->validate([
            'status' => 'required|in:active,suspended,deleted',
        ]);

        $user->update(['status' => $request->status]);

        return back()->with('success', "User status updated to {$request->status}.");
    }

    /**
     * Show transactions
     */
    public function transactions(Request $request)
    {
        $query = Transaction::with('user');

        // Filter by status
        if ($request->status) {
            $query->where('status', $request->status);
        }

        // Filter by type
        if ($request->type) {
            $query->where('type', $request->type);
        }

        // Date range filter
        if ($request->from_date) {
            $query->whereDate('created_at', '>=', $request->from_date);
        }
        if ($request->to_date) {
            $query->whereDate('created_at', '<=', $request->to_date);
        }

        $transactions = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('admin.transactions', compact('transactions'));
    }

    /**
     * Show tickets
     */
    public function tickets(Request $request)
    {
        $query = Ticket::with('user');

        // Filter by status
        if ($request->status) {
            $query->where('status', $request->status);
        }

        // Search by PNR or user
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('pnr', 'like', '%' . $request->search . '%')
                  ->orWhereHas('user', function ($userQuery) use ($request) {
                      $userQuery->where('name', 'like', '%' . $request->search . '%')
                               ->orWhere('email', 'like', '%' . $request->search . '%');
                  });
            });
        }

        $tickets = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('admin.tickets', compact('tickets'));
    }

    /**
     * Admin logout
     */
    public function logout(Request $request)
    {
        Auth::guard('admin')->logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('admin.login')->with('success', 'Successfully logged out!');
    }
}
