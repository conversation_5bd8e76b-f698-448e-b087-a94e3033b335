const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const Admin = require('../models/Admin');
const auth = require('../middleware/auth');
const User = require('../models/User');

// Admin login - POST /api/admin/login
router.post('/login', async (req, res) => {
    try {
        const { email, password } = req.body;
        console.log('Admin login attempt:', { email });

        // Validate input
        if (!email || !password) {
            console.log('Login failed: Missing email or password');
            return res.status(400).json({
                status: 'error',
                message: 'Please provide email and password'
            });
        }

        // Find admin
        const admin = await Admin.findOne({ email });
        if (!admin) {
            console.log('Login failed: Admin not found');
            return res.status(401).json({
                status: 'error',
                message: 'Invalid credentials'
            });
        }

        console.log('Admin found:', { id: admin._id, email: admin.email });

        // Check password
        const isMatch = await bcrypt.compare(password, admin.password);
        if (!isMatch) {
            console.log('Login failed: Password mismatch');
            return res.status(401).json({
                status: 'error',
                message: 'Invalid credentials'
            });
        }

        console.log('Password verified successfully');

        // Generate token
        const token = jwt.sign(
            { id: admin._id },
            process.env.JWT_SECRET || 'your-secret-key-here',
            { expiresIn: '1d' }
        );

        console.log('Token generated:', token.substring(0, 20) + '...');

        res.json({
            status: 'success',
            token,
            admin: {
                id: admin._id,
                name: admin.name,
                email: admin.email
            }
        });

    } catch (error) {
        console.error('Admin login error:', error);
        res.status(500).json({
            status: 'error',
            message: 'Server error'
        });
    }
});

// Get admin stats
router.get('/stats', auth, async (req, res) => {
    try {
        // Check if user is admin
        if (!req.isAdmin && !req.admin) {
            console.log('Admin access denied for stats - isAdmin:', req.isAdmin, 'admin object:', !!req.admin);
            return res.status(403).json({
                status: 'error',
                message: 'Admin access required'
            });
        }

        console.log('Admin access granted for stats');
        console.log('Admin details:', req.admin ? { id: req.admin._id, email: req.admin.email } : 'No admin object');

        const User = require('../models/User');

        console.log('Fetching stats...'); // Debug log
        console.log('Admin authenticated:', !!req.admin); // Debug log

        // Get total users count
        const totalUsers = await User.countDocuments();
        console.log('Total users:', totalUsers); // Debug log

        // Get active users count
        const activeUsers = await User.countDocuments({ status: 'active' });
        console.log('Active users:', activeUsers); // Debug log

        // Get users registered in last week
        const lastWeek = new Date();
        lastWeek.setDate(lastWeek.getDate() - 7);
        const lastWeekUsers = await User.countDocuments({
            createdAt: { $gte: lastWeek }
        });
        console.log('Last week users:', lastWeekUsers); // Debug log

        res.json({
            status: 'success',
            totalUsers,
            activeUsers,
            lastWeekUsers
        });
    } catch (error) {
        console.error('Stats error:', error);
        // Send more detailed error information
        res.status(500).json({
            status: 'error',
            message: 'Failed to fetch stats',
            error: error.message,
            stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
    }
});

// Get users list with pagination and optional filters
router.get('/users', auth, async (req, res) => {
    try {
        // Check if user is admin
        console.log('Auth check for users endpoint:', { isAdmin: req.isAdmin, hasAdminObj: !!req.admin });

        if (!req.isAdmin && !req.admin) {
            console.log('Admin access denied for users endpoint');
            return res.status(403).json({
                status: 'error',
                message: 'Admin access required'
            });
        }

        console.log('Admin access granted for users endpoint');
        console.log('Admin details:', req.admin ? { id: req.admin._id, email: req.admin.email } : 'No admin object');

        console.log('Admin access granted');

        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const skip = (page - 1) * limit;
        const status = req.query.status;

        console.log('Fetching users with params:', { page, limit, status }); // Debug log

        // Build query
        let query = {};
        if (status) {
            query.status = status;
        }

        // Get total count first
        const totalUsers = await User.countDocuments(query);
        console.log('Total users matching query:', totalUsers); // Debug log

        // Get users with pagination
        const users = await User.find(query)
            .select('name email status picture createdAt lastLogin googleId apiKey availableTickets')
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(limit)
            .lean(); // Use lean() for better performance

        console.log('Users found:', users.length); // Debug log

        if (!users || users.length === 0) {
            return res.status(404).json({
                status: 'error',
                message: 'No users found'
            });
        }

        // Format user data
        const formattedUsers = users.map(user => ({
            _id: user._id,
            name: user.name || 'N/A',
            email: user.email || 'N/A',
            status: user.status || 'inactive',
            picture: user.picture || '/img/default-avatar.png',
            createdAt: user.createdAt,
            lastLogin: user.lastLogin,
            googleId: user.googleId ? 'Connected' : 'Not Connected',
            apiKey: user.apiKey ? '••••' + user.apiKey.slice(-4) : 'None',
            availableTickets: user.availableTickets || 0
        }));

        res.json({
            status: 'success',
            users: formattedUsers,
            currentPage: page,
            totalPages: Math.ceil(totalUsers / limit),
            totalUsers
        });
    } catch (error) {
        console.error('Users list error:', error);
        res.status(500).json({
            status: 'error',
            message: 'Failed to fetch users',
            error: error.message
        });
    }
});

// Get single user details
router.get('/users/:id', auth, async (req, res) => {
    try {
        // Check if user is admin
        if (!req.isAdmin && !req.admin) {
            return res.status(403).json({
                status: 'error',
                message: 'Admin access required'
            });
        }

        const userId = req.params.id;

        // Validate userId
        if (!userId.match(/^[0-9a-fA-F]{24}$/)) {
            return res.status(400).json({
                status: 'error',
                message: 'Invalid user ID format'
            });
        }

        // Find user
        const user = await User.findById(userId).lean();

        if (!user) {
            return res.status(404).json({
                status: 'error',
                message: 'User not found'
            });
        }

        // Format user data
        const formattedUser = {
            _id: user._id,
            name: user.name || 'N/A',
            email: user.email || 'N/A',
            status: user.status || 'inactive',
            picture: user.picture || '/img/default-avatar.png',
            createdAt: user.createdAt,
            lastLogin: user.lastLogin,
            googleId: user.googleId ? 'Connected' : 'Not Connected',
            apiKey: user.apiKey ? '••••' + user.apiKey.slice(-4) : 'None',
            availableTickets: user.availableTickets || 0
        };

        res.json({
            status: 'success',
            user: formattedUser
        });
    } catch (error) {
        console.error('User details error:', error);
        res.status(500).json({
            status: 'error',
            message: 'Failed to fetch user details',
            error: error.message
        });
    }
});

// Update user
router.patch('/users/:id', auth, async (req, res) => {
    try {
        // Check if user is admin
        if (!req.isAdmin && !req.admin) {
            return res.status(403).json({
                status: 'error',
                message: 'Admin access required'
            });
        }

        const userId = req.params.id;
        const { ticketChange, status } = req.body;

        // Validate userId
        if (!userId.match(/^[0-9a-fA-F]{24}$/)) {
            return res.status(400).json({
                status: 'error',
                message: 'Invalid user ID format'
            });
        }

        // Find user
        const user = await User.findById(userId);

        if (!user) {
            return res.status(404).json({
                status: 'error',
                message: 'User not found'
            });
        }

        // Update user fields
        if (status && ['active', 'suspended', 'deleted'].includes(status)) {
            user.status = status;
        }

        if (ticketChange !== undefined) {
            // Ensure availableTickets doesn't go below 0
            const newTicketCount = Math.max(0, (user.availableTickets || 0) + ticketChange);
            user.availableTickets = newTicketCount;
        }

        await user.save();

        res.json({
            status: 'success',
            message: 'User updated successfully',
            user: {
                _id: user._id,
                status: user.status,
                availableTickets: user.availableTickets
            }
        });
    } catch (error) {
        console.error('User update error:', error);
        res.status(500).json({
            status: 'error',
            message: 'Failed to update user',
            error: error.message
        });
    }
});

module.exports = router;
