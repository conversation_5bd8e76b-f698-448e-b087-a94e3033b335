<?php

use App\Http\Controllers\Auth\SocialController;
use App\Http\Controllers\Web\AdminController;
use App\Http\Controllers\Web\DashboardController;
use App\Http\Controllers\Web\HomeController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Public routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/privacy', [HomeController::class, 'privacy'])->name('privacy');
Route::get('/terms', [HomeController::class, 'terms'])->name('terms');
Route::get('/support', [HomeController::class, 'support'])->name('support');

// Authentication routes
Route::prefix('auth')->group(function () {
    // Google OAuth
    Route::get('/google', [SocialController::class, 'redirectToGoogle'])->name('auth.google');
    Route::get('/google/callback', [SocialController::class, 'handleGoogleCallback'])->name('auth.google.callback');

    // Facebook OAuth
    Route::get('/facebook', [SocialController::class, 'redirectToFacebook'])->name('auth.facebook');
    Route::get('/facebook/callback', [SocialController::class, 'handleFacebookCallback'])->name('auth.facebook.callback');

    // Logout
    Route::post('/logout', [SocialController::class, 'logout'])->name('logout');
});

// Protected user routes
Route::middleware('auth')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/buy-credits', [DashboardController::class, 'buyCredits'])->name('buy-credits');
    Route::post('/payment/create-order', [DashboardController::class, 'createPaymentOrder'])->name('payment.create-order');
    Route::post('/payment/verify', [DashboardController::class, 'verifyPayment'])->name('payment.verify');
});

// Admin routes
Route::prefix('admin')->name('admin.')->group(function () {
    // Admin login
    Route::get('/login', [AdminController::class, 'showLogin'])->name('login');
    Route::post('/login', [AdminController::class, 'login'])->name('login.post');

    // Protected admin routes
    Route::middleware('admin')->group(function () {
        Route::get('/', [AdminController::class, 'dashboard'])->name('dashboard');
        Route::get('/users', [AdminController::class, 'users'])->name('users');
        Route::get('/users/{user}', [AdminController::class, 'userDetails'])->name('users.details');
        Route::post('/users/{user}/credits', [AdminController::class, 'addCredits'])->name('users.add-credits');
        Route::post('/users/{user}/status', [AdminController::class, 'updateUserStatus'])->name('users.update-status');
        Route::get('/transactions', [AdminController::class, 'transactions'])->name('transactions');
        Route::get('/tickets', [AdminController::class, 'tickets'])->name('tickets');
        Route::post('/logout', [AdminController::class, 'logout'])->name('logout');
    });
});
