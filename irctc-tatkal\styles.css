/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Roboto, Arial, sans-serif;
    background-color: #f0f2f5;
    color: #1a1a1a;
    width: 360px !important;
    height: 500px !important;
    overflow: hidden;
}

.container {
    height: 100%;
    padding: 12px;
}

/* Adjust user profile section */
.user-profile {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
}

.user-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
}

.user-details {
    font-size: 12px;
    line-height: 1.2;
}

.user-details h2 {
    font-size: 13px;
    margin: 0;
}

.user-details p {
    font-size: 11px;
    color: #666;
    margin: 2px 0 0;
}

/* Common form elements */
input[type="text"],
input[type="password"],
input[type="number"],
select {
    width: 100%;
    padding: 10px 12px;
    margin-bottom: 15px;
    border: 1.5px solid #d0d7de;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.2s ease;
}

input[type="text"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
select:focus {
    outline: none;
    border-color: #0969da;
    box-shadow: 0 0 0 3px rgba(9, 105, 218, 0.1);
}

/* Button styles */
.btn {
    background-color: #2ea44f;
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    width: 100%;
    transition: all 0.2s ease;
}

.btn:hover {
    background-color: #2c974b;
    transform: translateY(-1px);
}

.btn:active {
    transform: translateY(1px);
}

/* Headings */
h2 {
    color: #24292f;
    margin-bottom: 25px;
    font-size: 24px;
    font-weight: 600;
    text-align: center;
}

h3 {
    color: #57606a;
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: 500;
}

/* Form sections */
.form-section {
    margin-bottom: 25px;
    padding: 20px;
    border: 1px solid #e1e4e8;
    border-radius: 8px;
    background: white;
}

/* Utility classes */
.error {
    color: #cf222e;
    font-size: 12px;
    margin-top: 5px;
}

.loading {
    opacity: 0.7;
    pointer-events: none;
}

/* Custom checkbox styling */
input[type="checkbox"] {
    width: 18px;
    height: 18px;
    margin-right: 8px;
    vertical-align: middle;
    accent-color: #2ea44f;
}

/* Form section specific styles */
.form-section:nth-child(even) {
    background-color: #fcfcfd;
}

/* Responsive adjustments */
@media (max-width: 600px) {
    body {
        padding: 15px;
    }
    
    .container {
        padding: 20px;
    }
    
    .station-selection {
        flex-direction: column;
        gap: 10px;
    }
    
    h2 {
        font-size: 20px;
    }
}

/* Loading state for inputs */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

/* Error state for inputs */
.error {
    border-color: #cf222e !important;
}

.error-message {
    color: #cf222e;
    font-size: 12px;
    margin-top: -10px;
    margin-bottom: 10px;
}




