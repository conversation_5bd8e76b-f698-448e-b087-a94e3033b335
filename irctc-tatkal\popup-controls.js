document.addEventListener('DOMContentLoaded', function() {
    // Initialize automation controls
    initializeAutomationControls();
});

// Initialize automation controls
function initializeAutomationControls() {
    console.log('Initializing automation controls...');

    // Get current automation status
    chrome.runtime.sendMessage({ action: 'getAutomationStatus' }, function(response) {
        if (response && response.status === 'success') {
            updateAutomationStatusUI(response.automationStatus.state);
        } else {
            console.error('Failed to get automation status:', response);
        }
    });

    // Set up automation control button
    const toggleBtn = document.getElementById('toggle-automation-btn');

    // Toggle button (Pause/Resume)
    toggleBtn.addEventListener('click', function() {
        // Check current button state
        const currentState = toggleBtn.querySelector('i').classList.contains('fa-pause') ? 'running' : 'paused';

        if (currentState === 'running') {
            // Currently running, so pause
            chrome.runtime.sendMessage({ action: 'pauseAutomation' }, function(response) {
                if (response && response.status === 'success') {
                    updateAutomationStatusUI('paused');
                } else {
                    console.error('Failed to pause automation:', response);
                }
            });
        } else {
            // Currently paused, so resume
            chrome.runtime.sendMessage({ action: 'resumeAutomation' }, function(response) {
                if (response && response.status === 'success') {
                    updateAutomationStatusUI('running');
                } else {
                    console.error('Failed to resume automation:', response);
                }
            });
        }
    });
}

// Update automation status UI
function updateAutomationStatusUI(state) {
    const statusText = document.getElementById('status-text');
    const toggleBtn = document.getElementById('toggle-automation-btn');

    // Update status text
    statusText.textContent = state.charAt(0).toUpperCase() + state.slice(1);

    // Remove all status classes
    statusText.classList.remove('status-running', 'status-paused', 'status-stopped');

    // Add appropriate status class
    statusText.classList.add(`status-${state}`);

    // Update toggle button appearance and functionality
    if (state === 'running') {
        // Show pause icon and text
        toggleBtn.innerHTML = '<i class="fas fa-pause"></i> Pause';
        toggleBtn.classList.remove('btn-success');
        toggleBtn.classList.add('btn-warning');
        toggleBtn.disabled = false;
    } else if (state === 'paused') {
        // Show play/resume icon and text
        toggleBtn.innerHTML = '<i class="fas fa-play"></i> Resume';
        toggleBtn.classList.remove('btn-warning');
        toggleBtn.classList.add('btn-success');
        toggleBtn.disabled = false;
    } else if (state === 'stopped') {
        // We don't have a stopped state anymore, but keeping this for future compatibility
        toggleBtn.disabled = false;
        toggleBtn.innerHTML = '<i class="fas fa-play"></i> Resume';
        toggleBtn.classList.remove('btn-warning');
        toggleBtn.classList.add('btn-success');
    }
}
