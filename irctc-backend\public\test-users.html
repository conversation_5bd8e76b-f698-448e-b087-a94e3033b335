<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Users Table</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-7xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">Test Users Table</h1>
        
        <div class="bg-white shadow rounded-lg p-6">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User Info</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Account Details</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tickets</th>
                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="users-table-body" class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td colspan="6" class="text-center py-4">Loading test data...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('Test page loaded');
            const tbody = document.getElementById('users-table-body');
            
            try {
                // Create some test users
                const testUsers = [
                    {
                        _id: '1',
                        name: 'Test User 1',
                        email: '<EMAIL>',
                        status: 'active',
                        picture: '/img/default-avatar.png',
                        createdAt: new Date().toISOString(),
                        lastLogin: new Date().toISOString(),
                        googleId: 'Connected',
                        apiKey: '••••1234',
                        availableTickets: 5
                    },
                    {
                        _id: '2',
                        name: 'Test User 2',
                        email: '<EMAIL>',
                        status: 'suspended',
                        picture: '/img/default-avatar.png',
                        createdAt: new Date().toISOString(),
                        lastLogin: new Date().toISOString(),
                        googleId: 'Not Connected',
                        apiKey: '••••5678',
                        availableTickets: 0
                    }
                ];
                
                console.log('Test users created:', testUsers.length);
                tbody.innerHTML = '';
                
                testUsers.forEach((user, index) => {
                    try {
                        console.log(`Processing test user ${index + 1}:`, user.email);
                        const tr = document.createElement('tr');
                        tr.innerHTML = `
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" src="${user.picture}" alt="" onerror="this.src='/img/default-avatar.png'">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">${user.name}</div>
                                        <div class="text-sm text-gray-500">Joined: ${new Date(user.createdAt).toLocaleDateString()}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">${user.email}</div>
                                <div class="text-sm text-gray-500">Google: ${user.googleId}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <select class="status-select rounded-md border-gray-300 shadow-sm" data-user-id="${user._id}">
                                    <option value="active" ${user.status === 'active' ? 'selected' : ''}>Active</option>
                                    <option value="suspended" ${user.status === 'suspended' ? 'selected' : ''}>Suspended</option>
                                    <option value="deleted" ${user.status === 'deleted' ? 'selected' : ''}>Deleted</option>
                                </select>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">API Key: ${user.apiKey}</div>
                                <div class="text-sm text-gray-500">Last Login: ${new Date(user.lastLogin).toLocaleDateString()}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-center">
                                    <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        ${user.availableTickets}
                                    </span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                <a href="#" class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                                    View Details
                                </a>
                            </td>
                        `;
                        tbody.appendChild(tr);
                        console.log(`Test user ${user.email} row appended to table`);
                    } catch (error) {
                        console.error(`Error processing test user ${index + 1}:`, error);
                    }
                });
                
                console.log('All test users processed and added to table');
                
            } catch (error) {
                console.error('Error in test script:', error);
                tbody.innerHTML = `<tr><td colspan="6" class="text-center py-4 text-red-500">Error: ${error.message}</td></tr>`;
            }
        });
    </script>
</body>
</html>
