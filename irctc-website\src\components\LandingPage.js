import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { authAPI, handleApiError } from '../services/api';
import Layout from './Layout';
import '../styles/LandingPage.css';

const LandingPage = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    // Initialize Google Sign-In with proper loading check
    const initializeGoogle = () => {
      if (window.gapi && window.gapi.load) {
        window.gapi.load('auth2', () => {
          try {
            window.gapi.auth2.init({
              client_id: process.env.REACT_APP_GOOGLE_CLIENT_ID || 'YOUR_GOOGLE_CLIENT_ID'
            });
          } catch (error) {
            console.warn('Google Auth initialization failed:', error);
          }
        });
      }
    };

    // Initialize Facebook SDK with proper loading check
    const initializeFacebook = () => {
      if (window.FB && window.FB.init) {
        try {
          window.FB.init({
            appId: process.env.REACT_APP_FACEBOOK_APP_ID || 'YOUR_FACEBOOK_APP_ID',
            cookie: true,
            xfbml: true,
            version: 'v18.0'
          });
        } catch (error) {
          console.warn('Facebook SDK initialization failed:', error);
        }
      }
    };

    // Wait for scripts to load
    const checkAndInitialize = () => {
      initializeGoogle();
      initializeFacebook();
    };

    // Check if scripts are already loaded
    if (document.readyState === 'complete') {
      setTimeout(checkAndInitialize, 100);
    } else {
      window.addEventListener('load', checkAndInitialize);
      return () => window.removeEventListener('load', checkAndInitialize);
    }
  }, []);

  const handleGoogleLogin = async () => {
    setLoading(true);
    setError('');

    try {
      if (!window.gapi || !window.gapi.auth2) {
        setError('Google Sign-In is not available. Please refresh the page and try again.');
        setLoading(false);
        return;
      }

      const authInstance = window.gapi.auth2.getAuthInstance();
      if (!authInstance) {
        setError('Google Sign-In is not properly initialized. Please refresh the page.');
        setLoading(false);
        return;
      }

      const googleUser = await authInstance.signIn();
      const profile = googleUser.getBasicProfile();
      const authResponse = googleUser.getAuthResponse();

      const userData = {
        token: authResponse.access_token,
        name: profile.getName(),
        email: profile.getEmail(),
        googleId: profile.getId(),
        picture: profile.getImageUrl()
      };

      const response = await authAPI.googleLogin(userData);

      // Store auth data
      if (response.data.token) {
        localStorage.setItem('authToken', response.data.token);
        localStorage.setItem('user', JSON.stringify(response.data.user));
        navigate('/dashboard');
      } else {
        setError('Login failed. Please try again.');
      }
    } catch (error) {
      console.error('Google login error:', error);
      if (error.error === 'popup_closed_by_user') {
        setError('Login was cancelled. Please try again.');
      } else {
        setError(handleApiError(error));
      }
    } finally {
      setLoading(false);
    }
  };

  const handleFacebookLogin = () => {
    setLoading(true);
    setError('');

    if (!window.FB || !window.FB.login) {
      setError('Facebook Sign-In is not available. Please refresh the page and try again.');
      setLoading(false);
      return;
    }

    window.FB.login(async (response) => {
      if (response.authResponse) {
        try {
          // Get user profile
          window.FB.api('/me', { fields: 'name,email,picture' }, async (userInfo) => {
            const userData = {
              accessToken: response.authResponse.accessToken,
              name: userInfo.name,
              email: userInfo.email,
              facebookId: userInfo.id,
              picture: userInfo.picture?.data?.url
            };

            try {
              const apiResponse = await authAPI.facebookLogin(userData);

              // Store auth data
              if (apiResponse.data.token) {
                localStorage.setItem('authToken', apiResponse.data.token);
                localStorage.setItem('user', JSON.stringify(apiResponse.data.user));
                navigate('/dashboard');
              } else {
                setError('Facebook login failed. Please try again.');
              }
            } catch (error) {
              console.error('Facebook login API error:', error);
              setError(handleApiError(error));
            }
          });
        } catch (error) {
          console.error('Facebook profile error:', error);
          setError('Failed to get Facebook profile');
        }
      } else {
        setError('Facebook login was cancelled');
      }
      setLoading(false);
    }, { scope: 'email' });
  };

  return (
    <Layout>
      <div className="landing-page">
        <div className="landing-container">

        {/* Hero Section */}
        <section className="hero-section">
          <div className="hero-content">
            <h2>Book Tatkal Tickets Automatically</h2>
            <p>
              Get your IRCTC Tatkal tickets booked automatically with our Chrome extension.
              Purchase credits and let our extension handle the booking process for you.
            </p>

            {error && <div className="error-message">{error}</div>}

            <div className="auth-buttons">
              <button
                className="google-btn"
                onClick={handleGoogleLogin}
                disabled={loading}
              >
                <img src="/google-icon.svg" alt="Google" />
                {loading ? 'Signing in...' : 'Continue with Google'}
              </button>

              <button
                className="facebook-btn"
                onClick={handleFacebookLogin}
                disabled={loading}
              >
                <img src="/facebook-icon.svg" alt="Facebook" />
                {loading ? 'Signing in...' : 'Continue with Facebook'}
              </button>
            </div>
          </div>

          <div className="hero-image">
            <img src="/hero-train.svg" alt="Train booking illustration" />
          </div>
        </section>

        {/* Features Section */}
        <section className="features-section">
          <h3>Why Choose Our Extension?</h3>
          <div className="features-grid">
            <div className="feature-card">
              <div className="feature-icon">⚡</div>
              <h4>Lightning Fast</h4>
              <p>Book tickets in seconds with automated form filling and submission</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">🔒</div>
              <h4>Secure & Safe</h4>
              <p>Your data is encrypted and secure. We never store your payment details</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">🎯</div>
              <h4>High Success Rate</h4>
              <p>Advanced algorithms ensure maximum booking success during Tatkal hours</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">💳</div>
              <h4>Credit System</h4>
              <p>Buy credits once and use them for multiple bookings. No recurring charges</p>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="pricing-section">
          <h3>Simple Pricing</h3>
          <div className="pricing-cards">
            <div className="pricing-card">
              <h4>Starter Pack</h4>
              <div className="price">₹99</div>
              <div className="credits">5 Credits</div>
              <p>Perfect for occasional travelers</p>
              <button className="price-btn" disabled>Login to Purchase</button>
            </div>
            <div className="pricing-card popular">
              <div className="popular-badge">Most Popular</div>
              <h4>Value Pack</h4>
              <div className="price">₹299</div>
              <div className="credits">20 Credits</div>
              <p>Best value for regular travelers</p>
              <button className="price-btn" disabled>Login to Purchase</button>
            </div>
            <div className="pricing-card">
              <h4>Pro Pack</h4>
              <div className="price">₹499</div>
              <div className="credits">40 Credits</div>
              <p>For frequent business travelers</p>
              <button className="price-btn" disabled>Login to Purchase</button>
            </div>
          </div>
        </section>

        </div>
      </div>
    </Layout>
  );
};

export default LandingPage;
