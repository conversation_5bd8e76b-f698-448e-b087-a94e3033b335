:root {
    --primary-color: #4285f4;
    --primary-dark: #3367d6;
    --secondary-color: #34a853;
    --danger-color: #ea4335;
    --warning-color: #fbbc05;
    --text-color: #333;
    --dark-gray: #666;
    --medium-gray: #ccc;
    --light-gray: #f5f5f5;
    --border-radius: 4px;
    --box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: var(--text-color);
    line-height: 1.6;
    margin: 0;
    padding: 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header styles */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--medium-gray);
}

header h1 {
    font-size: 24px;
    color: var(--primary-color);
}

header h1 i {
    margin-right: 10px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.user-info div {
    display: flex;
    flex-direction: column;
}

#user-name {
    font-weight: 600;
}

#credits-badge {
    font-size: 14px;
    color: var(--secondary-color);
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Main content layout */
main {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
}

/* Credits card styles */
.credits-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
}

.card-header {
    margin-bottom: 20px;
}

.card-header h2 {
    color: var(--primary-color);
    margin-bottom: 5px;
}

.info-text {
    color: var(--dark-gray);
    font-size: 14px;
}

/* Plans container */
.plans-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.plan-card {
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius);
    padding: 20px;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    position: relative;
}

.plan-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.plan-card.popular {
    border-color: var(--primary-color);
    box-shadow: 0 5px 15px rgba(66, 133, 244, 0.2);
}

.popular-tag {
    position: absolute;
    top: -10px;
    right: 10px;
    background-color: var(--primary-color);
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.plan-header {
    text-align: center;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--medium-gray);
}

.plan-header h3 {
    margin-bottom: 10px;
    color: var(--text-color);
}

.price {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
}

.credits {
    font-size: 16px;
    color: var(--secondary-color);
    margin-top: 5px;
}

.plan-features {
    flex-grow: 1;
    margin-bottom: 20px;
}

.plan-features ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

.plan-features ul li {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.plan-features ul li i {
    color: var(--secondary-color);
}

.btn-select-plan {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    padding: 10px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.3s ease;
}

.btn-select-plan:hover {
    background-color: var(--primary-dark);
}

/* Custom plan */
.custom-plan {
    background-color: var(--light-gray);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 30px;
}

.custom-plan h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: var(--text-color);
}

.custom-plan-form {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.form-group {
    flex: 1;
    min-width: 150px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius);
}

.price-display {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
    min-width: 150px;
}

/* Checkout section */
.checkout-section {
    background-color: var(--light-gray);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-top: 20px;
}

.checkout-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: var(--text-color);
}

.checkout-summary {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 20px;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.summary-row:last-child {
    margin-bottom: 0;
    padding-top: 10px;
    border-top: 1px solid var(--medium-gray);
    font-weight: 600;
}

.payment-options {
    margin-bottom: 20px;
}

.payment-options h4 {
    margin-top: 0;
    margin-bottom: 10px;
}

.payment-methods {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.payment-method {
    flex: 1;
    min-width: 120px;
}

.payment-method input[type="radio"] {
    display: none;
}

.payment-method label {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
    border: 2px solid var(--medium-gray);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
}

.payment-method label i {
    font-size: 24px;
    margin-bottom: 8px;
    color: var(--dark-gray);
}

.payment-method input[type="radio"]:checked + label {
    border-color: var(--primary-color);
    background-color: rgba(66, 133, 244, 0.1);
}

.payment-method input[type="radio"]:checked + label i {
    color: var(--primary-color);
}

.checkout-actions {
    display: flex;
    justify-content: space-between;
}

/* Button styles */
.btn-primary, .btn-secondary {
    padding: 10px 20px;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
    font-size: 14px;
    border: none;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-secondary {
    background-color: var(--light-gray);
    color: var(--text-color);
}

.btn-secondary:hover {
    background-color: var(--medium-gray);
}

/* Sidebar styles */
.info-sidebar {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.info-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
}

.info-card h3 {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
    color: var(--primary-color);
}

.info-card p {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 14px;
}

.info-card p:last-child {
    margin-bottom: 0;
}

.support-link {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    color: var(--primary-color);
    text-decoration: none;
    margin-top: 10px;
}

.support-link:hover {
    text-decoration: underline;
}

/* Footer styles */
footer {
    margin-top: 40px;
    padding-top: 20px;
    border-top: 1px solid var(--medium-gray);
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.footer-links {
    display: flex;
    gap: 20px;
}

.footer-links a {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.footer-links a:hover {
    text-decoration: underline;
}

.copyright {
    font-size: 12px;
    color: var(--dark-gray);
}

/* Utility classes */
.hidden {
    display: none;
}

/* Responsive styles */
@media (max-width: 992px) {
    .plans-container {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    main {
        grid-template-columns: 1fr;
    }
    
    .plans-container {
        grid-template-columns: 1fr;
    }
    
    header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .checkout-actions {
        flex-direction: column;
        gap: 10px;
    }
    
    .btn-primary, .btn-secondary {
        width: 100%;
    }
}
