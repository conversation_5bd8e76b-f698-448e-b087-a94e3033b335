require('dotenv').config();
const fetch = require('node-fetch');

const testAdminLogin = async () => {
    try {
        console.log('Testing admin login...');
        
        const response = await fetch('http://localhost:3000/api/admin/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'admin123'
            })
        });
        
        const data = await response.json();
        console.log('Response status:', response.status);
        console.log('Response data:', data);
        
        if (data.token) {
            console.log('Login successful! Token:', data.token.substring(0, 20) + '...');
            
            // Test the stats endpoint with the token
            console.log('\nTesting stats endpoint with token...');
            const statsResponse = await fetch('http://localhost:3000/api/admin/stats', {
                headers: {
                    'Authorization': `<PERSON><PERSON> ${data.token}`
                }
            });
            
            const statsData = await statsResponse.json();
            console.log('Stats response status:', statsResponse.status);
            console.log('Stats response data:', statsData);
        }
    } catch (error) {
        console.error('Error testing admin login:', error);
    }
};

testAdminLogin();
