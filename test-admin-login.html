<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Admin Login</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h4>Test Admin Login</h4>
                    </div>
                    <div class="card-body">
                        <form id="loginForm">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" value="<EMAIL>" required>
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password" value="admin123" required>
                            </div>
                            <button type="submit" class="btn btn-primary">Test Login</button>
                        </form>
                        <div id="result" class="mt-3"></div>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header">
                        <h5>Quick Links</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="http://127.0.0.1:8000" class="btn btn-outline-primary" target="_blank">
                                Open Website
                            </a>
                            <a href="http://127.0.0.1:8000/admin/login" class="btn btn-outline-success" target="_blank">
                                Open Admin Login
                            </a>
                            <a href="http://127.0.0.1:8000/admin" class="btn btn-outline-warning" target="_blank">
                                Open Admin Dashboard (requires login)
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header">
                        <h5>Admin Credentials</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td><EMAIL></td>
                            </tr>
                            <tr>
                                <td><strong>Password:</strong></td>
                                <td>admin123</td>
                            </tr>
                            <tr>
                                <td><strong>Role:</strong></td>
                                <td>Super Admin</td>
                            </tr>
                        </table>
                        
                        <h6 class="mt-3">Support Admin:</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td><EMAIL></td>
                            </tr>
                            <tr>
                                <td><strong>Password:</strong></td>
                                <td>support123</td>
                            </tr>
                            <tr>
                                <td><strong>Role:</strong></td>
                                <td>Admin</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.innerHTML = '<div class="alert alert-info">Testing login...</div>';
            
            try {
                // First get CSRF token
                const csrfResponse = await fetch('http://127.0.0.1:8000/admin/login');
                const csrfText = await csrfResponse.text();
                const csrfMatch = csrfText.match(/name="csrf-token" content="([^"]+)"/);
                const csrfToken = csrfMatch ? csrfMatch[1] : '';
                
                if (!csrfToken) {
                    throw new Error('Could not get CSRF token');
                }
                
                // Attempt login
                const formData = new FormData();
                formData.append('email', email);
                formData.append('password', password);
                formData.append('_token', csrfToken);
                
                const response = await fetch('http://127.0.0.1:8000/admin/login', {
                    method: 'POST',
                    body: formData,
                    credentials: 'include'
                });
                
                if (response.ok) {
                    if (response.redirected) {
                        resultDiv.innerHTML = `
                            <div class="alert alert-success">
                                <strong>Login Successful!</strong><br>
                                Redirected to: ${response.url}<br>
                                <a href="${response.url}" target="_blank" class="btn btn-sm btn-success mt-2">
                                    Open Admin Dashboard
                                </a>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = '<div class="alert alert-success">Login successful!</div>';
                    }
                } else {
                    const errorText = await response.text();
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <strong>Login Failed!</strong><br>
                            Status: ${response.status}<br>
                            Response: ${errorText.substring(0, 200)}...
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Error:</strong> ${error.message}
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
