<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IRCTC Routes Tester</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .section h2 {
            color: #444;
            margin-top: 0;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .test-item {
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 6px;
            background: white;
        }
        .test-result {
            margin: 10px 0;
            padding: 12px;
            border-radius: 6px;
            border-left: 4px solid #ccc;
        }
        .success {
            background-color: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .warning {
            background-color: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        .info {
            background-color: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }
        button {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .status-pending { background-color: #6c757d; }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 IRCTC Routes Tester</h1>
        
        <div class="section">
            <h2>📊 Test Configuration</h2>
            <div class="test-grid">
                <div class="test-item">
                    <strong>🖥️ Backend (Render):</strong><br>
                    <code>https://ex-irctc.onrender.com</code>
                </div>
                <div class="test-item">
                    <strong>🌐 Frontend (Netlify):</strong><br>
                    <code id="frontend-url">https://your-netlify-app.netlify.app</code>
                    <br><small>Update this URL when you deploy</small>
                </div>
            </div>
            
            <button onclick="runAllTests()">🧪 Run All Tests</button>
            <button onclick="runBackendTests()">🖥️ Test Backend Only</button>
            <button onclick="runFrontendTests()">🌐 Test Frontend Only</button>
            <button onclick="clearResults()">🧹 Clear Results</button>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progress"></div>
            </div>
        </div>

        <div class="section">
            <h2>🖥️ Backend API Routes</h2>
            <div id="backend-results"></div>
        </div>

        <div class="section">
            <h2>🌐 Frontend Routes</h2>
            <div id="frontend-results"></div>
        </div>

        <div class="section">
            <h2>📋 Test Summary</h2>
            <div id="summary-results"></div>
        </div>
    </div>

    <script>
        const BACKEND_URL = 'https://ex-irctc.onrender.com';
        const FRONTEND_URL = 'https://your-netlify-app.netlify.app'; // Update this
        
        let totalTests = 0;
        let completedTests = 0;
        let passedTests = 0;
        let failedTests = 0;

        const backendRoutes = [
            { path: '/', description: 'Root endpoint', method: 'GET', expectedStatus: 200 },
            { path: '/health', description: 'Health check', method: 'GET', expectedStatus: 200 },
            { path: '/api', description: 'API documentation', method: 'GET', expectedStatus: 200 },
            { path: '/api/auth/google', description: 'Google OAuth (should fail without token)', method: 'POST', expectedStatus: 400 },
            { path: '/api/users/credits', description: 'User credits (should fail without auth)', method: 'GET', expectedStatus: 401 },
            { path: '/api/tickets/count', description: 'Ticket count (should fail without auth)', method: 'GET', expectedStatus: 401 },
            { path: '/api/payment/create-order', description: 'Create payment order (should fail without auth)', method: 'POST', expectedStatus: 401 },
            { path: '/api/admin/login', description: 'Admin login (should fail without credentials)', method: 'POST', expectedStatus: 400 },
            { path: '/login', description: 'Login page (HTML)', method: 'GET', expectedStatus: 200 },
            { path: '/dashboard', description: 'Dashboard page (HTML)', method: 'GET', expectedStatus: 200 },
            { path: '/nonexistent', description: 'Non-existent route (should 404)', method: 'GET', expectedStatus: 404 }
        ];

        const frontendRoutes = [
            { path: '/', description: 'Landing page' },
            { path: '/privacy', description: 'Privacy policy' },
            { path: '/terms', description: 'Terms of service' },
            { path: '/support', description: 'Support page' },
            { path: '/admin/login', description: 'Admin login' },
            { path: '/nonexistent', description: 'Non-existent route (should redirect)' }
        ];

        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            container.appendChild(div);
        }

        function updateProgress() {
            const progress = (completedTests / totalTests) * 100;
            document.getElementById('progress').style.width = `${progress}%`;
        }

        function clearResults() {
            document.getElementById('backend-results').innerHTML = '';
            document.getElementById('frontend-results').innerHTML = '';
            document.getElementById('summary-results').innerHTML = '';
            totalTests = 0;
            completedTests = 0;
            passedTests = 0;
            failedTests = 0;
            updateProgress();
        }

        async function testBackendRoute(route) {
            const url = `${BACKEND_URL}${route.path}`;
            try {
                const options = {
                    method: route.method,
                    headers: { 'Content-Type': 'application/json' }
                };

                if (route.method === 'POST') {
                    options.body = JSON.stringify({});
                }

                const response = await fetch(url, options);
                const data = await response.text();
                
                let parsedData;
                try {
                    parsedData = JSON.parse(data);
                } catch {
                    parsedData = data.substring(0, 200) + (data.length > 200 ? '...' : '');
                }

                const isExpectedStatus = response.status === route.expectedStatus;
                const status = isExpectedStatus ? 'success' : 'warning';
                
                if (isExpectedStatus) passedTests++;
                else failedTests++;

                addResult('backend-results', 
                    `<span class="status-indicator status-${status}"></span>
                     <strong>${route.method} ${route.path}</strong> - ${route.description}<br>
                     Status: ${response.status} ${isExpectedStatus ? '✅' : '⚠️'}<br>
                     <details><summary>Response</summary><pre>${JSON.stringify(parsedData, null, 2)}</pre></details>`, 
                    status
                );
            } catch (error) {
                failedTests++;
                addResult('backend-results', 
                    `<span class="status-indicator status-error"></span>
                     <strong>${route.method} ${route.path}</strong> - ${route.description}<br>
                     ❌ Network Error: ${error.message}`, 
                    'error'
                );
            }
            
            completedTests++;
            updateProgress();
        }

        async function testFrontendRoute(route) {
            const url = `${FRONTEND_URL}${route.path}`;
            try {
                const response = await fetch(url, { method: 'HEAD' });
                
                if (response.ok || response.status === 404) {
                    passedTests++;
                    addResult('frontend-results', 
                        `<span class="status-indicator status-success"></span>
                         <strong>${route.path}</strong> - ${route.description}<br>
                         Status: ${response.status} ✅ <a href="${url}" target="_blank">Open</a>`, 
                        'success'
                    );
                } else {
                    failedTests++;
                    addResult('frontend-results', 
                        `<span class="status-indicator status-warning"></span>
                         <strong>${route.path}</strong> - ${route.description}<br>
                         Status: ${response.status} ⚠️`, 
                        'warning'
                    );
                }
            } catch (error) {
                failedTests++;
                addResult('frontend-results', 
                    `<span class="status-indicator status-error"></span>
                     <strong>${route.path}</strong> - ${route.description}<br>
                     ❌ Error: ${error.message}`, 
                    'error'
                );
            }
            
            completedTests++;
            updateProgress();
        }

        async function runBackendTests() {
            addResult('backend-results', '🔄 Testing backend routes...', 'info');
            totalTests = backendRoutes.length;
            completedTests = 0;
            passedTests = 0;
            failedTests = 0;

            for (const route of backendRoutes) {
                await testBackendRoute(route);
                await new Promise(resolve => setTimeout(resolve, 300));
            }

            addResult('backend-results', `✅ Backend tests completed: ${passedTests} passed, ${failedTests} failed`, 'info');
        }

        async function runFrontendTests() {
            if (FRONTEND_URL.includes('your-netlify-app')) {
                addResult('frontend-results', '⚠️ Please update FRONTEND_URL with your actual Netlify URL', 'warning');
                return;
            }

            addResult('frontend-results', '🔄 Testing frontend routes...', 'info');
            const currentTotal = totalTests;
            totalTests += frontendRoutes.length;

            for (const route of frontendRoutes) {
                await testFrontendRoute(route);
                await new Promise(resolve => setTimeout(resolve, 300));
            }

            addResult('frontend-results', `✅ Frontend tests completed`, 'info');
        }

        async function runAllTests() {
            clearResults();
            addResult('summary-results', '🚀 Starting comprehensive route testing...', 'info');
            
            await runBackendTests();
            await runFrontendTests();
            
            const totalPassed = passedTests;
            const totalFailed = failedTests;
            const successRate = ((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1);
            
            addResult('summary-results', 
                `📊 <strong>Test Summary:</strong><br>
                 ✅ Passed: ${totalPassed}<br>
                 ❌ Failed: ${totalFailed}<br>
                 📈 Success Rate: ${successRate}%<br>
                 ${successRate >= 80 ? '🎉 Great! Most routes are working.' : '⚠️ Some routes need attention.'}`, 
                successRate >= 80 ? 'success' : 'warning'
            );
        }

        // Auto-run backend tests when page loads
        window.onload = () => {
            addResult('summary-results', '📡 Route tester ready! Click "Run All Tests" to begin.', 'info');
        };
    </script>
</body>
</html>
