<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Credits - IRCTC Auto Booking</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="styles/add-credits.css">
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-train"></i> IRCTC Auto Booking</h1>
            <div class="user-info" id="user-info">
                <img id="user-avatar" class="user-avatar" src="" alt="User Avatar">
                <div>
                    <span id="user-name">Loading...</span>
                    <span id="credits-badge"><i class="fas fa-ticket-alt"></i> <span id="credits-count">0</span> credits</span>
                </div>
            </div>
        </header>

        <main>
            <div class="credits-card">
                <div class="card-header">
                    <h2>Purchase Credits</h2>
                    <p class="info-text">Credits are used to book train tickets automatically. Each booking uses 1 credit.</p>
                </div>

                <div class="plans-container">
                    <div class="plan-card" data-plan="basic">
                        <div class="plan-header">
                            <h3>Basic</h3>
                            <div class="price">₹199</div>
                            <div class="credits">5 Credits</div>
                        </div>
                        <div class="plan-features">
                            <ul>
                                <li><i class="fas fa-check"></i> 5 Automatic Bookings</li>
                                <li><i class="fas fa-check"></i> Tatkal Booking Support</li>
                                <li><i class="fas fa-check"></i> Auto-fill Passenger Details</li>
                                <li><i class="fas fa-check"></i> Valid for 30 days</li>
                            </ul>
                        </div>
                        <button class="btn-select-plan" data-plan="basic" data-price="199" data-credits="5">Select Plan</button>
                    </div>

                    <div class="plan-card popular" data-plan="standard">
                        <div class="popular-tag">Most Popular</div>
                        <div class="plan-header">
                            <h3>Standard</h3>
                            <div class="price">₹499</div>
                            <div class="credits">15 Credits</div>
                        </div>
                        <div class="plan-features">
                            <ul>
                                <li><i class="fas fa-check"></i> 15 Automatic Bookings</li>
                                <li><i class="fas fa-check"></i> Tatkal Booking Support</li>
                                <li><i class="fas fa-check"></i> Auto-fill Passenger Details</li>
                                <li><i class="fas fa-check"></i> Auto Captcha Solving</li>
                                <li><i class="fas fa-check"></i> Valid for 60 days</li>
                            </ul>
                        </div>
                        <button class="btn-select-plan" data-plan="standard" data-price="499" data-credits="15">Select Plan</button>
                    </div>

                    <div class="plan-card" data-plan="premium">
                        <div class="plan-header">
                            <h3>Premium</h3>
                            <div class="price">₹999</div>
                            <div class="credits">35 Credits</div>
                        </div>
                        <div class="plan-features">
                            <ul>
                                <li><i class="fas fa-check"></i> 35 Automatic Bookings</li>
                                <li><i class="fas fa-check"></i> Tatkal Booking Support</li>
                                <li><i class="fas fa-check"></i> Auto-fill Passenger Details</li>
                                <li><i class="fas fa-check"></i> Auto Captcha Solving</li>
                                <li><i class="fas fa-check"></i> Priority Support</li>
                                <li><i class="fas fa-check"></i> Valid for 90 days</li>
                            </ul>
                        </div>
                        <button class="btn-select-plan" data-plan="premium" data-price="999" data-credits="35">Select Plan</button>
                    </div>
                </div>

                <div class="custom-plan">
                    <h3>Custom Plan</h3>
                    <div class="custom-plan-form">
                        <div class="form-group">
                            <label for="custom-credits">Number of Credits</label>
                            <input type="number" id="custom-credits" min="1" max="100" value="10">
                        </div>
                        <div class="price-display">
                            <span>Total Price: ₹<span id="custom-price">399</span></span>
                        </div>
                        <button id="btn-custom-plan" class="btn-select-plan">Purchase Custom Plan</button>
                    </div>
                </div>

                <div class="checkout-section hidden" id="checkout-section">
                    <h3>Checkout</h3>
                    <div class="checkout-summary">
                        <div class="summary-row">
                            <span>Plan:</span>
                            <span id="checkout-plan">Standard</span>
                        </div>
                        <div class="summary-row">
                            <span>Credits:</span>
                            <span id="checkout-credits">15</span>
                        </div>
                        <div class="summary-row">
                            <span>Price:</span>
                            <span id="checkout-price">₹499</span>
                        </div>
                    </div>
                    
                    <div class="payment-options">
                        <h4>Select Payment Method</h4>
                        <div class="payment-methods">
                            <div class="payment-method">
                                <input type="radio" id="payment-upi" name="payment-method" value="upi" checked>
                                <label for="payment-upi">
                                    <i class="fas fa-mobile-alt"></i>
                                    <span>UPI</span>
                                </label>
                            </div>
                            
                            <div class="payment-method">
                                <input type="radio" id="payment-card" name="payment-method" value="card">
                                <label for="payment-card">
                                    <i class="fas fa-credit-card"></i>
                                    <span>Credit/Debit Card</span>
                                </label>
                            </div>
                            
                            <div class="payment-method">
                                <input type="radio" id="payment-netbanking" name="payment-method" value="netbanking">
                                <label for="payment-netbanking">
                                    <i class="fas fa-university"></i>
                                    <span>Net Banking</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="checkout-actions">
                        <button type="button" id="btn-back" class="btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Plans
                        </button>
                        <button type="button" id="btn-proceed" class="btn-primary">
                            <i class="fas fa-shopping-cart"></i> Proceed to Payment
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="info-sidebar">
                <div class="info-card">
                    <h3><i class="fas fa-info-circle"></i> About Credits</h3>
                    <p>Credits are used to book train tickets automatically through our extension. Each successful booking consumes 1 credit from your account.</p>
                    <p>Credits are valid for a limited time depending on the plan you choose. Make sure to use them before they expire.</p>
                </div>
                
                <div class="info-card">
                    <h3><i class="fas fa-shield-alt"></i> Secure Payment</h3>
                    <p>All payments are processed securely through our payment gateway. Your payment information is never stored on our servers.</p>
                    <p>After successful payment, credits will be instantly added to your account.</p>
                </div>
                
                <div class="info-card">
                    <h3><i class="fas fa-question-circle"></i> Need Help?</h3>
                    <p>If you have any questions or issues with purchasing credits, please contact our support team.</p>
                    <a href="mailto:<EMAIL>" class="support-link">
                        <i class="fas fa-envelope"></i> <EMAIL>
                    </a>
                </div>
            </div>
        </main>
        
        <footer>
            <div class="footer-links">
                <a href="dashboard.html"><i class="fas fa-home"></i> Dashboard</a>
                <a href="#" id="help-link"><i class="fas fa-question-circle"></i> Help</a>
                <a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
            <div class="copyright">
                &copy; 2023 IRCTC Auto Booking Extension
            </div>
        </footer>
    </div>
    
    <script src="add-credits.js"></script>
</body>
</html>
