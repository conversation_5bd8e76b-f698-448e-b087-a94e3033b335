import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { adminAPI, getCurrentAdmin, adminLogout, handleApiError } from '../services/api';
import '../styles/UserManagement.css';

const UserManagement = () => {
  const [admin] = useState(getCurrentAdmin());
  const [users, setUsers] = useState([]);
  const [selectedUser, setSelectedUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalUsers: 0
  });
  const [filters, setFilters] = useState({
    search: '',
    status: 'all'
  });
  const [showAddCreditsModal, setShowAddCreditsModal] = useState(false);
  const [creditsToAdd, setCreditsToAdd] = useState('');
  
  const navigate = useNavigate();
  const { userId } = useParams();

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  useEffect(() => {
    if (userId) {
      fetchUserDetails(userId);
    }
  }, [userId]);

  const fetchUsers = useCallback(async () => {
    try {
      setLoading(true);
      const params = {
        page: pagination.currentPage,
        limit: 10,
        ...filters
      };

      if (filters.search) {
        params.search = filters.search;
      }
      if (filters.status !== 'all') {
        params.status = filters.status;
      }

      const response = await adminAPI.getUsers(params);
      setUsers(response.data.users || []);
      setPagination({
        currentPage: response.data.currentPage,
        totalPages: response.data.totalPages,
        totalUsers: response.data.totalUsers
      });
    } catch (error) {
      console.error('Error fetching users:', error);
      setError(handleApiError(error));
    } finally {
      setLoading(false);
    }
  }, [pagination.currentPage, filters]);

  const fetchUserDetails = async (id) => {
    try {
      const response = await adminAPI.getUser(id);
      setSelectedUser(response.data.user);
    } catch (error) {
      console.error('Error fetching user details:', error);
      setError(handleApiError(error));
    }
  };

  const handleAddCredits = async () => {
    if (!selectedUser || !creditsToAdd) return;

    try {
      const credits = parseInt(creditsToAdd);
      if (isNaN(credits) || credits <= 0) {
        setError('Please enter a valid number of credits');
        return;
      }

      await adminAPI.addCredits(selectedUser._id, credits);
      setSuccess(`Successfully added ${credits} credits to ${selectedUser.name}`);
      setShowAddCreditsModal(false);
      setCreditsToAdd('');
      
      // Refresh user details and users list
      fetchUserDetails(selectedUser._id);
      fetchUsers();
      
      setTimeout(() => setSuccess(''), 3000);
    } catch (error) {
      console.error('Error adding credits:', error);
      setError(handleApiError(error));
    }
  };

  const handleStatusChange = async (userId, newStatus) => {
    try {
      await adminAPI.updateUserStatus(userId, newStatus);
      setSuccess(`User status updated to ${newStatus}`);
      
      // Refresh data
      fetchUsers();
      if (selectedUser && selectedUser._id === userId) {
        fetchUserDetails(userId);
      }
      
      setTimeout(() => setSuccess(''), 3000);
    } catch (error) {
      console.error('Error updating user status:', error);
      setError(handleApiError(error));
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters({ ...filters, [key]: value });
    setPagination({ ...pagination, currentPage: 1 });
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="user-management">
      {/* Header */}
      <header className="admin-header">
        <div className="header-content">
          <div className="admin-info">
            <h1>User Management</h1>
            <p>Manage users, credits, and account status</p>
          </div>
          <div className="header-actions">
            <button 
              className="dashboard-btn"
              onClick={() => navigate('/admin')}
            >
              Dashboard
            </button>
            <button className="logout-btn" onClick={adminLogout}>
              Logout
            </button>
          </div>
        </div>
      </header>

      <div className="management-content">
        {error && <div className="alert alert-error">{error}</div>}
        {success && <div className="alert alert-success">{success}</div>}

        <div className="management-layout">
          {/* Users List */}
          <div className="users-section">
            {/* Filters */}
            <div className="filters-section">
              <div className="search-box">
                <input
                  type="text"
                  placeholder="Search users by name or email..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                />
              </div>
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="suspended">Suspended</option>
                <option value="deleted">Deleted</option>
              </select>
            </div>

            {/* Users Table */}
            {loading ? (
              <div className="loading-section">
                <div className="loading-spinner"></div>
                <p>Loading users...</p>
              </div>
            ) : (
              <>
                <div className="users-table">
                  <table>
                    <thead>
                      <tr>
                        <th>User</th>
                        <th>Email</th>
                        <th>Credits</th>
                        <th>Status</th>
                        <th>Joined</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {users.map((user) => (
                        <tr 
                          key={user._id}
                          className={selectedUser?._id === user._id ? 'selected' : ''}
                        >
                          <td>
                            <div className="user-cell">
                              <img 
                                src={user.picture || '/default-avatar.png'} 
                                alt={user.name}
                                className="user-avatar-small"
                              />
                              <span>{user.name}</span>
                            </div>
                          </td>
                          <td>{user.email}</td>
                          <td>
                            <span className="credits-badge">
                              {user.availableTickets}
                            </span>
                          </td>
                          <td>
                            <select
                              value={user.status}
                              onChange={(e) => handleStatusChange(user._id, e.target.value)}
                              className={`status-select ${user.status}`}
                            >
                              <option value="active">Active</option>
                              <option value="suspended">Suspended</option>
                              <option value="deleted">Deleted</option>
                            </select>
                          </td>
                          <td>{formatDate(user.createdAt)}</td>
                          <td>
                            <button 
                              className="action-btn"
                              onClick={() => {
                                setSelectedUser(user);
                                navigate(`/admin/users/${user._id}`);
                              }}
                            >
                              View Details
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Pagination */}
                <div className="pagination">
                  <button
                    disabled={pagination.currentPage === 1}
                    onClick={() => setPagination({ ...pagination, currentPage: pagination.currentPage - 1 })}
                  >
                    Previous
                  </button>
                  <span>
                    Page {pagination.currentPage} of {pagination.totalPages} 
                    ({pagination.totalUsers} total users)
                  </span>
                  <button
                    disabled={pagination.currentPage === pagination.totalPages}
                    onClick={() => setPagination({ ...pagination, currentPage: pagination.currentPage + 1 })}
                  >
                    Next
                  </button>
                </div>
              </>
            )}
          </div>

          {/* User Details */}
          {selectedUser && (
            <div className="user-details-section">
              <div className="user-details-card">
                <div className="user-header">
                  <img 
                    src={selectedUser.picture || '/default-avatar.png'} 
                    alt={selectedUser.name}
                    className="user-avatar-large"
                  />
                  <div className="user-info">
                    <h3>{selectedUser.name}</h3>
                    <p>{selectedUser.email}</p>
                    <span className={`status-badge ${selectedUser.status}`}>
                      {selectedUser.status}
                    </span>
                  </div>
                </div>

                <div className="user-stats">
                  <div className="stat-item">
                    <label>Available Credits</label>
                    <span className="stat-value">{selectedUser.availableTickets}</span>
                  </div>
                  <div className="stat-item">
                    <label>Google Connected</label>
                    <span className="stat-value">{selectedUser.googleId}</span>
                  </div>
                  <div className="stat-item">
                    <label>API Key</label>
                    <span className="stat-value">{selectedUser.apiKey}</span>
                  </div>
                  <div className="stat-item">
                    <label>Last Login</label>
                    <span className="stat-value">
                      {selectedUser.lastLogin ? formatDate(selectedUser.lastLogin) : 'Never'}
                    </span>
                  </div>
                  <div className="stat-item">
                    <label>Member Since</label>
                    <span className="stat-value">{formatDate(selectedUser.createdAt)}</span>
                  </div>
                </div>

                <div className="user-actions">
                  <button 
                    className="add-credits-btn"
                    onClick={() => setShowAddCreditsModal(true)}
                  >
                    Add Credits
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Add Credits Modal */}
      {showAddCreditsModal && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h3>Add Credits</h3>
              <button 
                className="close-btn"
                onClick={() => setShowAddCreditsModal(false)}
              >
                ×
              </button>
            </div>
            <div className="modal-body">
              <p>Add credits to {selectedUser?.name}'s account</p>
              <input
                type="number"
                placeholder="Number of credits to add"
                value={creditsToAdd}
                onChange={(e) => setCreditsToAdd(e.target.value)}
                min="1"
              />
            </div>
            <div className="modal-footer">
              <button 
                className="cancel-btn"
                onClick={() => setShowAddCreditsModal(false)}
              >
                Cancel
              </button>
              <button 
                className="confirm-btn"
                onClick={handleAddCredits}
              >
                Add Credits
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserManagement;
