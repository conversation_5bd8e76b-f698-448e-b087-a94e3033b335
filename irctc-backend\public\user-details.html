<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Details - IRCTC Tatkal Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen">
        <!-- Navigation -->
        <nav class="bg-white shadow-lg">
            <div class="max-w-7xl mx-auto px-4">
                <div class="flex justify-between h-16">
                    <div class="flex">
                        <div class="flex-shrink-0 flex items-center">
                            <h1 class="text-xl font-bold">IRCTC Tatkal Admin</h1>
                        </div>
                        <div class="ml-6 flex items-center">
                            <a href="/dashboard" class="text-gray-600 hover:text-gray-900">Dashboard</a>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <button id="logout-btn" class="text-gray-600 hover:text-gray-900">Logout</button>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                    <div>
                        <h3 class="text-lg leading-6 font-medium text-gray-900">User Details</h3>
                        <p class="mt-1 max-w-2xl text-sm text-gray-500">Personal details and account information.</p>
                    </div>
                    <div>
                        <a href="/dashboard" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                            </svg>
                            Back to Dashboard
                        </a>
                    </div>
                </div>
                <div class="border-t border-gray-200">
                    <div id="loading-indicator" class="px-4 py-5 sm:p-6 text-center">
                        <p class="text-gray-500">Loading user details...</p>
                    </div>
                    <div id="user-details" class="hidden">
                        <dl>
                            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">Full name</dt>
                                <dd id="user-name" class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">-</dd>
                            </div>
                            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">Email address</dt>
                                <dd id="user-email" class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">-</dd>
                            </div>
                            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">Account status</dt>
                                <dd id="user-status" class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">-</dd>
                            </div>
                            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">Available tickets</dt>
                                <dd id="user-tickets" class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">-</dd>
                            </div>
                            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">API Key</dt>
                                <dd id="user-apikey" class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">-</dd>
                            </div>
                            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">Account created</dt>
                                <dd id="user-created" class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">-</dd>
                            </div>
                            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">Last login</dt>
                                <dd id="user-lastlogin" class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">-</dd>
                            </div>
                        </dl>
                    </div>
                    <div id="error-message" class="hidden px-4 py-5 sm:p-6 bg-red-50 text-red-700 border-t border-red-200">
                        <p>Error loading user details. Please try again.</p>
                    </div>
                </div>

                <!-- Ticket Management Section -->
                <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Ticket Management</h3>
                    <div class="flex space-x-4">
                        <div class="flex-1">
                            <label for="ticket-amount" class="block text-sm font-medium text-gray-700 mb-2">Add/Remove Tickets</label>
                            <div class="flex rounded-md shadow-sm">
                                <input type="number" name="ticket-amount" id="ticket-amount" class="focus:ring-indigo-500 focus:border-indigo-500 flex-1 block w-full rounded-none rounded-l-md sm:text-sm border-gray-300" placeholder="1">
                                <button id="add-tickets-btn" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-r-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                    Add
                                </button>
                            </div>
                        </div>
                        <div class="flex-1">
                            <label for="status-select" class="block text-sm font-medium text-gray-700 mb-2">Update Status</label>
                            <select id="status-select" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                <option value="active">Active</option>
                                <option value="suspended">Suspended</option>
                                <option value="deleted">Deleted</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-4">
                        <button id="update-user-btn" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Update User
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Get user ID from URL
        const urlParams = new URLSearchParams(window.location.search);
        const userId = urlParams.get('id');
        let token = localStorage.getItem('adminToken');

        // Check authentication
        if (!token) {
            window.location.href = '/login';
        }

        // Fetch user details
        async function fetchUserDetails() {
            try {
                document.getElementById('loading-indicator').classList.remove('hidden');
                document.getElementById('user-details').classList.add('hidden');
                document.getElementById('error-message').classList.add('hidden');

                if (!userId) {
                    throw new Error('User ID is required');
                }

                const response = await fetch(`/api/admin/users/${userId}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch user details');
                }

                const data = await response.json();

                if (data.status !== 'success' || !data.user) {
                    throw new Error('Invalid response format');
                }

                // Update UI with user details
                const user = data.user;
                document.getElementById('user-name').textContent = user.name || 'N/A';
                document.getElementById('user-email').textContent = user.email || 'N/A';
                document.getElementById('user-status').textContent = user.status || 'N/A';
                document.getElementById('user-tickets').textContent = user.availableTickets || '0';
                document.getElementById('user-apikey').textContent = user.apiKey || 'None';
                document.getElementById('user-created').textContent = user.createdAt ? new Date(user.createdAt).toLocaleString() : 'N/A';
                document.getElementById('user-lastlogin').textContent = user.lastLogin ? new Date(user.lastLogin).toLocaleString() : 'Never';

                // Set current status in dropdown
                document.getElementById('status-select').value = user.status || 'active';

                // Show user details
                document.getElementById('loading-indicator').classList.add('hidden');
                document.getElementById('user-details').classList.remove('hidden');

            } catch (error) {
                console.error('Error fetching user details:', error);
                document.getElementById('loading-indicator').classList.add('hidden');
                document.getElementById('error-message').classList.remove('hidden');
                document.getElementById('error-message').querySelector('p').textContent = error.message;
            }
        }

        // Update user
        async function updateUser() {
            try {
                const ticketAmount = parseInt(document.getElementById('ticket-amount').value) || 0;
                const status = document.getElementById('status-select').value;

                if (!userId) {
                    throw new Error('User ID is required');
                }

                const response = await fetch(`/api/admin/users/${userId}`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        ticketChange: ticketAmount,
                        status
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.message || 'Failed to update user');
                }

                // Refresh user details
                fetchUserDetails();

                // Show success message
                alert('User updated successfully');

            } catch (error) {
                console.error('Error updating user:', error);
                alert(`Error: ${error.message}`);
            }
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', () => {
            fetchUserDetails();

            document.getElementById('update-user-btn').addEventListener('click', updateUser);

            document.getElementById('logout-btn').addEventListener('click', () => {
                localStorage.removeItem('adminToken');
                window.location.href = '/login';
            });
        });
    </script>
</body>
</html>
