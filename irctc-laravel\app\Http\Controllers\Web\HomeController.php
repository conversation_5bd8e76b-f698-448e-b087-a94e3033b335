<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Show the landing page
     */
    public function index()
    {
        $stats = [
            'total_users' => User::count(),
            'active_users' => User::where('status', 'active')->count(),
            'total_bookings' => User::sum('available_tickets'),
        ];

        return view('home.index', compact('stats'));
    }

    /**
     * Show privacy policy page
     */
    public function privacy()
    {
        return view('home.privacy');
    }

    /**
     * Show terms and conditions page
     */
    public function terms()
    {
        return view('home.terms');
    }

    /**
     * Show support page
     */
    public function support()
    {
        return view('home.support');
    }
}
