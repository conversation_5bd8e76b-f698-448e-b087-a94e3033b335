<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Laravel\Socialite\Facades\Socialite;

class SocialController extends Controller
{
    /**
     * Redirect to Google OAuth
     */
    public function redirectToGoogle()
    {
        return Socialite::driver('google')->redirect();
    }

    /**
     * Handle Google OAuth callback
     */
    public function handleGoogleCallback()
    {
        try {
            $googleUser = Socialite::driver('google')->user();

            // Find or create user
            $user = User::where('google_id', $googleUser->id)
                       ->orWhere('email', $googleUser->email)
                       ->first();

            if (!$user) {
                $user = User::create([
                    'name' => $googleUser->name,
                    'email' => $googleUser->email,
                    'google_id' => $googleUser->id,
                    'picture' => $googleUser->avatar,
                    'email_verified_at' => now(),
                    'api_key' => Str::random(32),
                ]);
            } else {
                // Update Google ID if not set
                if (!$user->google_id) {
                    $user->update(['google_id' => $googleUser->id]);
                }
                $user->update(['last_login' => now()]);
            }

            Auth::login($user);

            return redirect()->route('dashboard')->with('success', 'Successfully logged in with Google!');

        } catch (\Exception $e) {
            return redirect()->route('home')->with('error', 'Google authentication failed. Please try again.');
        }
    }

    /**
     * Redirect to Facebook OAuth
     */
    public function redirectToFacebook()
    {
        return Socialite::driver('facebook')->redirect();
    }

    /**
     * Handle Facebook OAuth callback
     */
    public function handleFacebookCallback()
    {
        try {
            $facebookUser = Socialite::driver('facebook')->user();

            // Find or create user
            $user = User::where('facebook_id', $facebookUser->id)
                       ->orWhere('email', $facebookUser->email)
                       ->first();

            if (!$user) {
                $user = User::create([
                    'name' => $facebookUser->name,
                    'email' => $facebookUser->email,
                    'facebook_id' => $facebookUser->id,
                    'picture' => $facebookUser->avatar,
                    'email_verified_at' => now(),
                    'api_key' => Str::random(32),
                ]);
            } else {
                // Update Facebook ID if not set
                if (!$user->facebook_id) {
                    $user->update(['facebook_id' => $facebookUser->id]);
                }
                $user->update(['last_login' => now()]);
            }

            Auth::login($user);

            return redirect()->route('dashboard')->with('success', 'Successfully logged in with Facebook!');

        } catch (\Exception $e) {
            return redirect()->route('home')->with('error', 'Facebook authentication failed. Please try again.');
        }
    }

    /**
     * Logout user
     */
    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('home')->with('success', 'Successfully logged out!');
    }
}
