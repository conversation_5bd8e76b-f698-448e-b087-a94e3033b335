@extends('layouts.admin')

@section('title', 'Transactions Management')
@section('page-title', 'Transactions Management')

@section('content')
<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <form method="GET" action="{{ route('admin.transactions') }}" class="row g-3">
                    <div class="col-md-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Status</option>
                            <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>Completed</option>
                            <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="failed" {{ request('status') === 'failed' ? 'selected' : '' }}>Failed</option>
                            <option value="cancelled" {{ request('status') === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="type" class="form-label">Type</label>
                        <select class="form-select" id="type" name="type">
                            <option value="">All Types</option>
                            <option value="credit_purchase" {{ request('type') === 'credit_purchase' ? 'selected' : '' }}>Credit Purchase</option>
                            <option value="refund" {{ request('type') === 'refund' ? 'selected' : '' }}>Refund</option>
                            <option value="bonus" {{ request('type') === 'bonus' ? 'selected' : '' }}>Bonus</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="from_date" class="form-label">From Date</label>
                        <input type="date" class="form-control" id="from_date" name="from_date" value="{{ request('from_date') }}">
                    </div>
                    <div class="col-md-2">
                        <label for="to_date" class="form-label">To Date</label>
                        <input type="date" class="form-control" id="to_date" name="to_date" value="{{ request('to_date') }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Filter
                            </button>
                            <a href="{{ route('admin.transactions') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Clear
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card success">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4>₹{{ number_format($transactions->where('status', 'completed')->sum('amount'), 2) }}</h4>
                    <p>Total Revenue</p>
                </div>
                <div class="text-success">
                    <i class="fas fa-rupee-sign fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4>{{ $transactions->where('status', 'completed')->count() }}</h4>
                    <p>Completed</p>
                </div>
                <div class="text-success">
                    <i class="fas fa-check-circle fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card warning">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4>{{ $transactions->whereIn('status', ['created', 'pending'])->count() }}</h4>
                    <p>Pending</p>
                </div>
                <div class="text-warning">
                    <i class="fas fa-clock fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card danger">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4>{{ $transactions->whereIn('status', ['failed', 'cancelled'])->count() }}</h4>
                    <p>Failed</p>
                </div>
                <div class="text-danger">
                    <i class="fas fa-times-circle fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Transactions Table -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>Transactions ({{ $transactions->total() }})</h5>
                <div class="d-flex gap-2">
                    <button class="btn btn-sm btn-success" onclick="exportTransactions()">
                        <i class="fas fa-download me-2"></i>Export
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                @if($transactions->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Transaction ID</th>
                                    <th>User</th>
                                    <th>Amount</th>
                                    <th>Credits</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($transactions as $transaction)
                                    <tr>
                                        <td>
                                            <code>{{ $transaction->order_id }}</code>
                                            @if($transaction->payment_id)
                                                <br><small class="text-muted">{{ $transaction->payment_id }}</small>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if($transaction->user->picture)
                                                    <img src="{{ $transaction->user->picture }}" alt="Profile" class="rounded-circle me-2" width="32" height="32">
                                                @else
                                                    <div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;">
                                                        <i class="fas fa-user fa-sm"></i>
                                                    </div>
                                                @endif
                                                <div>
                                                    <div class="fw-bold">{{ $transaction->user->name }}</div>
                                                    <small class="text-muted">{{ $transaction->user->email }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="fw-bold">₹{{ number_format($transaction->amount, 2) }}</span>
                                            <br><small class="text-muted">{{ $transaction->currency }}</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-info fs-6">{{ $transaction->credits }}</span>
                                        </td>
                                        <td>
                                            @switch($transaction->type)
                                                @case('credit_purchase')
                                                    <span class="badge bg-primary">Purchase</span>
                                                    @break
                                                @case('refund')
                                                    <span class="badge bg-warning">Refund</span>
                                                    @break
                                                @case('bonus')
                                                    <span class="badge bg-success">Bonus</span>
                                                    @break
                                                @default
                                                    <span class="badge bg-secondary">{{ ucfirst($transaction->type) }}</span>
                                            @endswitch
                                        </td>
                                        <td>
                                            @switch($transaction->status)
                                                @case('completed')
                                                    <span class="badge bg-success">Completed</span>
                                                    @break
                                                @case('pending')
                                                    <span class="badge bg-warning">Pending</span>
                                                    @break
                                                @case('failed')
                                                    <span class="badge bg-danger">Failed</span>
                                                    @break
                                                @case('cancelled')
                                                    <span class="badge bg-secondary">Cancelled</span>
                                                    @break
                                                @default
                                                    <span class="badge bg-secondary">{{ ucfirst($transaction->status) }}</span>
                                            @endswitch
                                        </td>
                                        <td>
                                            <div>{{ $transaction->created_at->format('M d, Y') }}</div>
                                            <small class="text-muted">{{ $transaction->created_at->format('h:i A') }}</small>
                                            <br><small class="text-muted">{{ $transaction->created_at->diffForHumans() }}</small>
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    Actions
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <button class="dropdown-item" onclick="viewTransaction('{{ $transaction->id }}')">
                                                            <i class="fas fa-eye me-2"></i>View Details
                                                        </button>
                                                    </li>
                                                    @if($transaction->status === 'pending')
                                                        <li>
                                                            <button class="dropdown-item text-success" onclick="updateTransactionStatus('{{ $transaction->id }}', 'completed')">
                                                                <i class="fas fa-check me-2"></i>Mark Completed
                                                            </button>
                                                        </li>
                                                        <li>
                                                            <button class="dropdown-item text-danger" onclick="updateTransactionStatus('{{ $transaction->id }}', 'failed')">
                                                                <i class="fas fa-times me-2"></i>Mark Failed
                                                            </button>
                                                        </li>
                                                    @endif
                                                    @if($transaction->type === 'credit_purchase' && $transaction->status === 'completed')
                                                        <li>
                                                            <button class="dropdown-item text-warning" onclick="refundTransaction('{{ $transaction->id }}')">
                                                                <i class="fas fa-undo me-2"></i>Refund
                                                            </button>
                                                        </li>
                                                    @endif
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="card-footer bg-white">
                        {{ $transactions->links() }}
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No transactions found</h5>
                        <p class="text-muted">No transactions match your current filters.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Transaction Details Modal -->
<div class="modal fade" id="transactionModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Transaction Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="transactionDetails">
                <!-- Transaction details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function exportTransactions() {
    // Implement export functionality
    alert('Export functionality will be implemented soon.');
}

function viewTransaction(transactionId) {
    // Load transaction details
    document.getElementById('transactionDetails').innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>';
    
    const modal = new bootstrap.Modal(document.getElementById('transactionModal'));
    modal.show();
    
    // Simulate loading transaction details
    setTimeout(() => {
        document.getElementById('transactionDetails').innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>Transaction Information</h6>
                    <table class="table table-sm">
                        <tr><td><strong>ID:</strong></td><td>${transactionId}</td></tr>
                        <tr><td><strong>Status:</strong></td><td><span class="badge bg-success">Completed</span></td></tr>
                        <tr><td><strong>Amount:</strong></td><td>₹500.00</td></tr>
                        <tr><td><strong>Credits:</strong></td><td>10</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>Payment Information</h6>
                    <table class="table table-sm">
                        <tr><td><strong>Payment ID:</strong></td><td>pay_123456789</td></tr>
                        <tr><td><strong>Method:</strong></td><td>UPI</td></tr>
                        <tr><td><strong>Gateway:</strong></td><td>Razorpay</td></tr>
                        <tr><td><strong>Date:</strong></td><td>Dec 28, 2024</td></tr>
                    </table>
                </div>
            </div>
        `;
    }, 1000);
}

function updateTransactionStatus(transactionId, status) {
    if (confirm(`Are you sure you want to mark this transaction as ${status}?`)) {
        // Implement status update
        alert(`Transaction ${transactionId} marked as ${status}`);
        location.reload();
    }
}

function refundTransaction(transactionId) {
    if (confirm('Are you sure you want to refund this transaction? This action cannot be undone.')) {
        // Implement refund functionality
        alert(`Refund initiated for transaction ${transactionId}`);
        location.reload();
    }
}
</script>
@endpush
