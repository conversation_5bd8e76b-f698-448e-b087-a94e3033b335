// Environment Configuration for Chrome Extension
// Auto-generated by build-config.js - DO NOT EDIT MANUALLY

const CONFIG = {
    development: {
        API_BASE_URL: 'http://localhost:3000/api',
        PAYMENT_URL: 'http://localhost:3000/payment',
        ENVIRONMENT: 'development'
    },
    production: {
        API_BASE_URL: 'https://ex-irctc.onrender.com/api',
        PAYMENT_URL: 'https://ex-irctc.onrender.com/payment',
        ENVIRONMENT: 'production'
    }
};

// Current environment configuration
const CURRENT_ENV = 'production';
const API_CONFIG = CONFIG[CURRENT_ENV];

// Helper functions
const getApiUrl = (endpoint) => {
    return `${API_CONFIG.API_BASE_URL}${endpoint}`;
};

const getPaymentUrl = (params = '') => {
    return `${API_CONFIG.PAYMENT_URL}${params}`;
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { API_CONFIG, getApiUrl, getPaymentUrl };
} else {
    // For browser environment
    window.API_CONFIG = API_CONFIG;
    window.getApiUrl = getApiUrl;
    window.getPaymentUrl = getPaymentUrl;
}

console.log('🚀 Chrome Extension loaded with', CURRENT_ENV, 'configuration');
console.log('📡 API Base URL:', API_CONFIG.API_BASE_URL);
console.log('💳 Payment URL:', API_CONFIG.PAYMENT_URL);
