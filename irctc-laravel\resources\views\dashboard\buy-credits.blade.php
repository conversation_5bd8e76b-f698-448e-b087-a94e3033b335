@extends('layouts.app')

@section('title', 'Buy Credits - IRCTC Auto Booking')

@section('content')
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">Buy Credits</h2>
                    <p class="text-muted mb-0">Purchase credits to book train tickets automatically</p>
                </div>
                <div class="text-end">
                    <div class="bg-primary text-white rounded p-3">
                        <h5 class="mb-1">Current Balance</h5>
                        <h3 class="mb-0">{{ $user->available_tickets }} Credits</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Credit Packages -->
    <div class="row g-4 mb-5">
        @foreach($packages as $index => $package)
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm h-100 {{ $package['popular'] ? 'border-primary shadow-lg' : '' }}">
                    @if($package['popular'])
                        <div class="card-header bg-primary text-white text-center border-0">
                            <span class="badge bg-warning text-dark">Most Popular</span>
                        </div>
                    @endif
                    
                    <div class="card-body text-center p-4">
                        <h5 class="card-title">{{ $package['name'] }}</h5>
                        <div class="display-6 fw-bold text-primary mb-2">₹{{ number_format($package['price']) }}</div>
                        <p class="text-muted mb-3">{{ $package['credits'] }} Credits</p>
                        
                        @if(isset($package['savings']))
                            <div class="alert alert-success py-2 mb-3">
                                <small><i class="fas fa-tag me-1"></i>Save ₹{{ $package['savings'] }}</small>
                            </div>
                        @endif
                        
                        <ul class="list-unstyled mb-4">
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>{{ $package['credits'] }} Ticket Bookings</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>24/7 Support</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Auto-fill Forms</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Instant Activation</li>
                            @if($package['popular'])
                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Priority Support</li>
                            @endif
                        </ul>
                        
                        <button class="btn {{ $package['popular'] ? 'btn-primary' : 'btn-outline-primary' }} w-100 buy-credits-btn" 
                                data-package="{{ $package['name'] }}"
                                data-credits="{{ $package['credits'] }}"
                                data-price="{{ $package['price'] }}">
                            <i class="fas fa-shopping-cart me-2"></i>Buy Now
                        </button>
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    <!-- Payment Methods -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>Secure Payment Methods</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3 mb-3">
                            <div class="p-3">
                                <i class="fab fa-cc-visa fa-3x text-primary mb-2"></i>
                                <p class="mb-0">Visa</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="p-3">
                                <i class="fab fa-cc-mastercard fa-3x text-danger mb-2"></i>
                                <p class="mb-0">Mastercard</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="p-3">
                                <i class="fas fa-university fa-3x text-success mb-2"></i>
                                <p class="mb-0">Net Banking</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="p-3">
                                <i class="fas fa-mobile-alt fa-3x text-info mb-2"></i>
                                <p class="mb-0">UPI</p>
                            </div>
                        </div>
                    </div>
                    <div class="text-center mt-3">
                        <small class="text-muted">
                            <i class="fas fa-shield-alt me-1"></i>
                            Powered by Razorpay - 256-bit SSL encrypted payments
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- FAQ Section -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0"><i class="fas fa-question-circle me-2"></i>Frequently Asked Questions</h5>
                </div>
                <div class="card-body">
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item border-0 mb-3">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    What are credits and how do they work?
                                </button>
                            </h2>
                            <div id="faq1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Credits are used to book train tickets automatically. Each credit allows you to book one ticket. Credits are deducted only when a ticket is successfully booked.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item border-0 mb-3">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                    Do credits expire?
                                </button>
                            </h2>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    No, credits never expire. You can use them whenever you need to book tickets.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item border-0 mb-3">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                    What if my booking fails?
                                </button>
                            </h2>
                            <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    If a booking fails due to technical issues or unavailability, your credit will be refunded automatically to your account.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item border-0">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                                    Is my payment information secure?
                                </button>
                            </h2>
                            <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Yes, all payments are processed through Razorpay with 256-bit SSL encryption. We never store your payment information on our servers.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Processing Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title">Processing Payment</h5>
            </div>
            <div class="modal-body text-center py-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mb-0">Please wait while we process your payment...</p>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const buyButtons = document.querySelectorAll('.buy-credits-btn');
    
    buyButtons.forEach(button => {
        button.addEventListener('click', function() {
            const packageName = this.dataset.package;
            const credits = parseInt(this.dataset.credits);
            const price = parseInt(this.dataset.price);
            
            // Disable button and show loading
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
            
            // Create payment order
            fetch('{{ route("payment.create-order") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    amount: price,
                    credits: credits
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Initialize Razorpay payment
                    const options = {
                        key: data.key,
                        amount: data.amount,
                        currency: data.currency,
                        name: data.name,
                        description: data.description,
                        order_id: data.order_id,
                        prefill: data.prefill,
                        theme: {
                            color: '#3399cc'
                        },
                        handler: function(response) {
                            // Show processing modal
                            const modal = new bootstrap.Modal(document.getElementById('paymentModal'));
                            modal.show();
                            
                            // Verify payment
                            fetch('{{ route("payment.verify") }}', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                                },
                                body: JSON.stringify({
                                    razorpay_order_id: response.razorpay_order_id,
                                    razorpay_payment_id: response.razorpay_payment_id,
                                    razorpay_signature: response.razorpay_signature
                                })
                            })
                            .then(response => response.json())
                            .then(data => {
                                modal.hide();
                                
                                if (data.success) {
                                    // Show success message and redirect
                                    alert(`Payment successful! ${data.credits} credits have been added to your account.`);
                                    window.location.href = data.redirect;
                                } else {
                                    alert('Payment verification failed: ' + data.message);
                                }
                            })
                            .catch(error => {
                                modal.hide();
                                alert('Payment verification failed. Please contact support.');
                                console.error('Error:', error);
                            });
                        },
                        modal: {
                            ondismiss: function() {
                                // Reset button
                                button.disabled = false;
                                button.innerHTML = '<i class="fas fa-shopping-cart me-2"></i>Buy Now';
                            }
                        }
                    };
                    
                    const rzp = new Razorpay(options);
                    rzp.open();
                    
                    // Reset button
                    this.disabled = false;
                    this.innerHTML = '<i class="fas fa-shopping-cart me-2"></i>Buy Now';
                } else {
                    alert('Failed to create payment order: ' + data.message);
                    // Reset button
                    this.disabled = false;
                    this.innerHTML = '<i class="fas fa-shopping-cart me-2"></i>Buy Now';
                }
            })
            .catch(error => {
                alert('Payment failed. Please try again.');
                console.error('Error:', error);
                // Reset button
                this.disabled = false;
                this.innerHTML = '<i class="fas fa-shopping-cart me-2"></i>Buy Now';
            });
        });
    });
});
</script>
@endpush
