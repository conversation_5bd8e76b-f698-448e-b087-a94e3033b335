let user_data = {};

// Listen for messages from the extension
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('Message received in content script:', message);
    
    if (message.action === 'startBookingProcess') {
        startBookingProcess();
        sendResponse({ status: 'success' });
    }
    
    return true; // Required for async sendResponse
});

// Check if we need to start the booking process when the page loads
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on the IRCTC website
    if (window.location.href.includes('irctc.co.in')) {
        chrome.storage.local.get([
            'irctc_credentials',
            'journey_details',
            'passenger_details',
            'infant_details',
            'other_preferences',
            'travel_preferences'
        ], function(data) {
            // Check if we have booking data
            if (data.irctc_credentials && data.journey_details && data.passenger_details) {
                console.log('Booking data found, starting booking process...');
                user_data = data;
                startBookingProcess();
            }
        });
    }
});

// Helper function to add delay
function addDelay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Helper function to translate class codes to display text
function classTranslator(classCode) {
    let labletext;
    if (classCode === "1A") labletext = "AC First Class (1A)";
    else if (classCode === "EV") labletext = "Vistadome AC (EV)";
    else if (classCode === "EC") labletext = "Exec. Chair Car (EC)";
    else if (classCode === "2A") labletext = "AC 2 Tier (2A)";
    else if (classCode === "3A") labletext = "AC 3 Tier (3A)";
    else if (classCode === "3E") labletext = "AC 3 Economy (3E)";
    else if (classCode === "CC") labletext = "AC Chair car (CC)";
    else if (classCode === "SL") labletext = "Sleeper (SL)";
    else if (classCode === "2S") labletext = "Second Sitting (2S)";
    else labletext = "None";
    return labletext;
}

// Helper function to translate quota codes to display text
function quotaTranslator(quotaCode) {
    let labletext;
    if (quotaCode === "GN") labletext = "GENERAL";
    else if (quotaCode === "TQ") labletext = "TATKAL";
    else if (quotaCode === "PT") labletext = "PREMIUM TATKAL";
    else if (quotaCode === "LD") labletext = "LADIES";
    else if (quotaCode === "SR") labletext = "LOWER BERTH/SR.CITIZEN";
    else labletext = quotaCode;
    return labletext;
}

// Start the booking process
async function startBookingProcess() {
    console.log('Starting booking process...');
    
    // Check which page we're on and take appropriate action
    if (window.location.href.includes('train-search')) {
        // We're on the train search page
        const loginButton = document.querySelector("body > app-root > app-home > div.header-fix > app-header > div.col-sm-12.h_container > div.text-center.h_main_div > div.row.col-sm-12.h_head1 > a.search_btn.loginText.ng-star-inserted");
        
        if (loginButton && loginButton.innerText.trim().toUpperCase() === "LOGIN") {
            // We need to login first
            loginButton.click();
            await addDelay(1000);
            await loadLoginDetails();
        } else if (loginButton && loginButton.innerText.trim().toUpperCase() === "LOGOUT") {
            // We're already logged in, load journey details
            await loadJourneyDetails();
        }
    } else if (window.location.href.includes('nget/booking/train-list')) {
        // We're on the train list page, select the train
        await selectJourney();
    } else if (window.location.href.includes('nget/booking/passenger-input')) {
        // We're on the passenger input page
        await fillPassengerDetails();
    } else if (window.location.href.includes('nget/booking/review')) {
        // We're on the review booking page
        await reviewBooking();
    } else if (window.location.href.includes('nget/booking/psgn-payment')) {
        // We're on the payment page
        await bkgPaymentOptions();
    }
}

// Load login details
async function loadLoginDetails() {
    console.log('Loading login details...');
    
    // Wait for the login form to appear
    await waitForElement("#divMain > app-login", 10);
    
    const loginForm = document.querySelector("#divMain > app-login");
    if (!loginForm) {
        console.error('Login form not found');
        return;
    }
    
    // Wait for the username and password fields to appear
    await waitForElement("input[type='text'][formcontrolname='userid']", 5, loginForm);
    await waitForElement("input[type='password'][formcontrolname='password']", 5, loginForm);
    
    const usernameInput = loginForm.querySelector("input[type='text'][formcontrolname='userid']");
    const passwordInput = loginForm.querySelector("input[type='password'][formcontrolname='password']");
    
    if (usernameInput && passwordInput) {
        // Fill username
        usernameInput.value = user_data.irctc_credentials.user_name || "";
        usernameInput.dispatchEvent(new Event('input'));
        usernameInput.dispatchEvent(new Event('change'));
        
        // Fill password
        passwordInput.value = user_data.irctc_credentials.password || "";
        passwordInput.dispatchEvent(new Event('input'));
        passwordInput.dispatchEvent(new Event('change'));
        
        // Handle captcha
        if (user_data.other_preferences && user_data.other_preferences.autoCaptcha) {
            await addDelay(500);
            await handleCaptcha();
        } else {
            console.log('Manual captcha filling');
            const captchaInput = document.querySelector("#captcha");
            if (captchaInput) {
                captchaInput.focus();
            }
        }
    } else {
        console.error('Username or password input not found');
    }
}

// Handle captcha
async function handleCaptcha() {
    console.log('Handling captcha...');
    
    // Wait for captcha image to load
    await waitForElement(".captcha-img", 10);
    
    // Focus on captcha input
    const captchaInput = document.querySelector("#captcha");
    if (captchaInput) {
        captchaInput.focus();
        
        // If auto-submit is enabled, set up a listener for captcha input
        if (user_data.other_preferences && user_data.other_preferences.CaptchaSubmitMode === 'A') {
            captchaInput.addEventListener('input', async function() {
                if (captchaInput.value.length >= 4) {
                    await addDelay(500);
                    submitLoginForm();
                }
            });
        }
    }
}

// Submit login form
function submitLoginForm() {
    console.log('Submitting login form...');
    
    const loginForm = document.querySelector("#divMain > app-login");
    if (!loginForm) {
        console.error('Login form not found');
        return;
    }
    
    // Try different selectors for the submit button
    const submitButton = 
        loginForm.querySelector("button[type='submit'][class='search_btn train_Search']") || 
        loginForm.querySelector("button[type='submit'][class='search_btn train_Search train_Search_custom_hover']") ||
        loginForm.querySelector("button[type='submit']");
    
    if (submitButton) {
        console.log('Submit button found, clicking...');
        submitButton.click();
    } else {
        console.error('Submit button not found');
    }
}

// Load journey details
async function loadJourneyDetails() {
    console.log('Loading journey details...');
    
    // Wait for the journey form to appear
    await waitForElement("app-jp-input form", 10);
    
    const journeyForm = document.querySelector("app-jp-input form");
    if (!journeyForm) {
        console.error('Journey form not found');
        return;
    }
    
    // Fill from station
    const fromStationInput = journeyForm.querySelector("#origin > span > input");
    if (fromStationInput) {
        fromStationInput.value = user_data.journey_details.from;
        fromStationInput.dispatchEvent(new Event('keydown'));
        fromStationInput.dispatchEvent(new Event('input'));
        await addDelay(300);
    }
    
    // Fill to station
    const toStationInput = journeyForm.querySelector("#destination > span > input");
    if (toStationInput) {
        toStationInput.value = user_data.journey_details.destination;
        toStationInput.dispatchEvent(new Event('keydown'));
        toStationInput.dispatchEvent(new Event('input'));
        await addDelay(300);
    }
    
    // Fill journey date
    const journeyDateInput = journeyForm.querySelector("#jDate > span > input");
    if (journeyDateInput) {
        const dateParts = user_data.journey_details.date.split('-');
        const formattedDate = `${dateParts[2]}/${dateParts[1]}/${dateParts[0]}`;
        journeyDateInput.value = formattedDate;
        journeyDateInput.dispatchEvent(new Event('keydown'));
        journeyDateInput.dispatchEvent(new Event('input'));
        await addDelay(300);
    }
    
    // Select class
    const classDropdown = journeyForm.querySelector("#journeyClass");
    if (classDropdown) {
        classDropdown.querySelector("div > div[role='button']").click();
        await addDelay(300);
        const classOptions = [...classDropdown.querySelectorAll("ul li")];
        const classToSelect = classOptions.find(option => 
            option.innerText === classTranslator(user_data.journey_details.class)
        );
        if (classToSelect) {
            classToSelect.click();
        }
        await addDelay(300);
    }
    
    // Select quota
    const quotaDropdown = journeyForm.querySelector("#journeyQuota");
    if (quotaDropdown) {
        quotaDropdown.querySelector("div > div[role='button']").click();
        await addDelay(300);
        const quotaOptions = [...quotaDropdown.querySelectorAll("ul li")];
        const quotaToSelect = quotaOptions.find(option => 
            option.innerText === quotaTranslator(user_data.journey_details.quota)
        );
        if (quotaToSelect) {
            quotaToSelect.click();
        }
        await addDelay(300);
    }
    
    // Click search button
    const searchButton = journeyForm.querySelector("button.search_btn.train_Search[type='submit']");
    if (searchButton) {
        await addDelay(500);
        console.log('Clicking search button...');
        searchButton.click();
    }
}

// Select journey
async function selectJourney() {
    console.log('Selecting journey...');
    
    // Wait for the train list to load
    await waitForElement("#divMain > div > app-train-list", 20);
    
    // Check if we need to wait for tatkal booking time
    if (user_data.journey_details.quota === 'TQ' || user_data.journey_details.quota === 'PT') {
        const classCode = user_data.journey_details.class;
        let requiredTime = "00:00:00";
        
        // Set the required time based on class
        if (['1A', '2A', '3A', 'CC', 'EC', '3E'].includes(classCode.toUpperCase())) {
            requiredTime = user_data.other_preferences.acbooktime || "10:00:00";
        } else {
            requiredTime = user_data.other_preferences.slbooktime || "11:00:00";
        }
        
        const currentTime = new Date().toString().split(" ")[4];
        console.log('Current time:', currentTime);
        console.log('Required time:', requiredTime);
        
        if (currentTime < requiredTime) {
            // We need to wait
            console.log('Waiting for tatkal booking time...');
            
            // Create a waiting message
            try {
                const waitingMessage = document.createElement('div');
                waitingMessage.textContent = "Please wait..Booking will automatically start at " + requiredTime;
                waitingMessage.style.textAlign = "center";
                waitingMessage.style.color = "white";
                waitingMessage.style.height = "auto";
                waitingMessage.style.fontSize = "20px";
                waitingMessage.style.backgroundColor = "green";
                waitingMessage.style.padding = "10px";
                waitingMessage.style.margin = "10px 0";
                
                const trainListHeader = document.querySelector("#divMain > div > app-train-list > div > div > div > div.clearfix");
                if (trainListHeader) {
                    trainListHeader.insertAdjacentElement("afterend", waitingMessage);
                }
            } catch (error) {
                console.error('Error creating waiting message:', error);
            }
            
            // Set up an interval to check the time
            const waitInterval = setInterval(() => {
                const newCurrentTime = new Date().toString().split(" ")[4];
                if (newCurrentTime >= requiredTime) {
                    clearInterval(waitInterval);
                    console.log('Time reached, proceeding with booking...');
                    selectTrainAndContinue();
                }
            }, 500);
            
            return;
        }
    }
    
    // If we're not waiting for tatkal time, proceed immediately
    await selectTrainAndContinue();
}

// Select train and continue
async function selectTrainAndContinue() {
    console.log('Selecting train and continuing...');
    
    const trainListContainer = document.querySelector("#divMain > div > app-train-list");
    if (!trainListContainer) {
        console.error('Train list container not found');
        return;
    }
    
    const trainCards = Array.from(trainListContainer.querySelectorAll(".tbis-div app-train-avl-enq"));
    const trainNumber = user_data.journey_details["train-no"];
    
    console.log('Looking for train number:', trainNumber);
    
    const trainCard = trainCards.find(card => 
        card.querySelector("div.train-heading").innerText.trim().includes(trainNumber.split("-")[0])
    );
    
    if (!trainCard) {
        console.error('Train not found');
        alert('Train not found. Please select manually.');
        return;
    }
    
    console.log('Train found, selecting class...');
    
    const classToSelect = classTranslator(user_data.journey_details.class);
    const classElement = Array.from(trainCard.querySelectorAll("table tr td div.pre-avl"))
        .find(div => div.querySelector("div").innerText === classToSelect);
    
    if (!classElement) {
        console.error('Class not found');
        alert('Selected class not available in the train. Please select manually.');
        return;
    }
    
    // Click on the class
    classElement.click();
    await addDelay(1000);
    
    // Get the date string
    const dateString = new Date(user_data.journey_details.date).toString().split(" ");
    const formattedDate = `${dateString[0]}, ${dateString[2]} ${dateString[1]}`;
    
    // Find and click the date element
    const dateElement = Array.from(trainCard.querySelectorAll("div div table td div.pre-avl"))
        .find(div => div.querySelector("div").innerText === formattedDate);
    
    if (dateElement) {
        dateElement.click();
        await addDelay(1000);
        
        // Find and click the book button
        const bookButton = trainCard.querySelector("button.btnDefault.train_Search.ng-star-inserted");
        if (bookButton && !bookButton.disabled) {
            bookButton.click();
        } else {
            console.warn('Book button is disabled or not found');
        }
    } else {
        console.error('Date element not found');
    }
}

// Fill passenger details
async function fillPassengerDetails() {
    console.log('Filling passenger details...');
    
    // Wait for the passenger form to load
    await waitForElement("app-passenger-input", 20);
    
    const passengerForm = document.querySelector("app-passenger-input");
    if (!passengerForm) {
        console.error('Passenger form not found');
        return;
    }
    
    // Set boarding station if different from origin
    if (user_data.journey_details.boarding && user_data.journey_details.boarding.length > 0) {
        console.log('Setting boarding station:', user_data.journey_details.boarding);
        
        const stationElements = document.getElementsByTagName("strong");
        const originStationElement = Array.from(stationElements)
            .find(el => el.innerText.includes(user_data.journey_details.from.split("-")[0].trim() + " | "));
        
        if (originStationElement) {
            originStationElement.click();
            await addDelay(300);
            
            const boardingStationElement = Array.from(document.getElementsByTagName("strong"))
                .find(el => el.innerText.includes(user_data.journey_details.boarding.split("-")[0].trim()));
            
            if (boardingStationElement) {
                boardingStationElement.click();
            }
        }
    }
    
    // Add additional passengers if needed
    const addPassengerButton = document.getElementsByClassName("prenext")[0];
    for (let i = 1; i < user_data.passenger_details.length; i++) {
        await addDelay(200);
        addPassengerButton.click();
    }
    
    // Add infants if needed
    try {
        const addInfantButton = document.getElementsByClassName("prenext")[2];
        for (let i = 0; i < user_data.infant_details.length; i++) {
            await addDelay(200);
            addInfantButton.click();
        }
    } catch (error) {
        console.error('Error adding infants:', error);
    }
    
    // Fill passenger details
    const passengerCards = [...passengerForm.querySelectorAll("app-passenger")];
    for (let index = 0; index < user_data.passenger_details.length; index++) {
        const passenger = user_data.passenger_details[index];
        if (index < passengerCards.length) {
            const card = passengerCards[index];
            
            // Fill name
            const nameInput = card.querySelector("p-autocomplete > span > input");
            if (nameInput) {
                nameInput.value = passenger.name;
                nameInput.dispatchEvent(new Event('input'));
                await addDelay(200);
            }
            
            // Fill age
            const ageInput = card.querySelector("input[type='number'][formcontrolname='passengerAge']");
            if (ageInput) {
                ageInput.value = passenger.age;
                ageInput.dispatchEvent(new Event('input'));
                await addDelay(200);
            }
            
            // Select gender
            const genderSelect = card.querySelector("select[formcontrolname='passengerGender']");
            if (genderSelect) {
                genderSelect.value = passenger.gender;
                genderSelect.dispatchEvent(new Event('change'));
                await addDelay(200);
            }
            
            // Select berth preference
            const berthSelect = card.querySelector("select[formcontrolname='passengerBerthChoice']");
            if (berthSelect) {
                berthSelect.value = passenger.berth;
                berthSelect.dispatchEvent(new Event('change'));
                await addDelay(200);
            }
            
            // Select food preference
            const foodSelect = card.querySelector("select[formcontrolname='passengerFoodChoice']");
            if (foodSelect) {
                foodSelect.value = passenger.food;
                foodSelect.dispatchEvent(new Event('change'));
                await addDelay(200);
            }
        }
    }
    
    // Fill infant details
    const infantCards = [...passengerForm.querySelectorAll("app-infant")];
    if (user_data.infant_details && user_data.infant_details.length > 0) {
        for (let index = 0; index < user_data.infant_details.length; index++) {
            const infant = user_data.infant_details[index];
            if (index < infantCards.length) {
                const card = infantCards[index];
                
                // Fill name
                const nameInput = card.querySelector("input#infant-name[name='infant-name']");
                if (nameInput) {
                    nameInput.value = infant.name;
                    nameInput.dispatchEvent(new Event('input'));
                    await addDelay(200);
                }
                
                // Select age
                const ageSelect = card.querySelector("select[formcontrolname='age']");
                if (ageSelect) {
                    ageSelect.value = infant.age;
                    ageSelect.dispatchEvent(new Event('change'));
                    await addDelay(200);
                }
                
                // Select gender
                const genderSelect = card.querySelector("select[formcontrolname='gender']");
                if (genderSelect) {
                    genderSelect.value = infant.gender;
                    genderSelect.dispatchEvent(new Event('change'));
                    await addDelay(200);
                }
            }
        }
    }
    
    // Fill mobile number
    if (user_data.other_preferences.mobileNumber) {
        const mobileInput = passengerForm.querySelector("input#mobileNumber[formcontrolname='mobileNumber'][name='mobileNumber']");
        if (mobileInput) {
            mobileInput.value = user_data.other_preferences.mobileNumber;
            mobileInput.dispatchEvent(new Event('input'));
            await addDelay(200);
        }
    }
    
    // Set auto upgradation
    const autoUpgradeCheckbox = passengerForm.querySelector("input#autoUpgradation[type='checkbox'][formcontrolname='autoUpgradationSelected']");
    if (autoUpgradeCheckbox) {
        autoUpgradeCheckbox.checked = user_data.other_preferences.autoUpgradation ?? false;
        autoUpgradeCheckbox.dispatchEvent(new Event('change'));
        await addDelay(200);
    }
    
    // Set confirm berths only
    const confirmBerthsCheckbox = passengerForm.querySelector("input#confirmberths[type='checkbox'][formcontrolname='bookOnlyIfCnf']");
    if (confirmBerthsCheckbox) {
        confirmBerthsCheckbox.checked = user_data.other_preferences.confirmberths ?? false;
        confirmBerthsCheckbox.dispatchEvent(new Event('change'));
        await addDelay(200);
    }
    
    // Set travel insurance
    const travelInsuranceRadios = [...passengerForm.querySelectorAll("p-radiobutton[formcontrolname='travelInsuranceOpted'] input[type='radio'][name='travelInsuranceOpted-0']")];
    const insuranceValue = user_data.travel_preferences.travelInsuranceOpted === "yes" ? "true" : "false";
    const insuranceRadio = travelInsuranceRadios.find(radio => radio.value === insuranceValue);
    if (insuranceRadio) {
        insuranceRadio.click();
        await addDelay(200);
    }
    
    // Set preferred coach
    try {
        const coachInput = passengerForm.querySelector("input[formcontrolname='coachId']");
        if (coachInput && user_data.travel_preferences.prefcoach) {
            coachInput.value = user_data.travel_preferences.prefcoach;
            coachInput.dispatchEvent(new Event('input'));
            await addDelay(200);
        }
    } catch (error) {
        console.error('Error setting preferred coach:', error);
    }
    
    // Submit the form
    await addDelay(1000);
    const submitButton = passengerForm.querySelector("#psgn-form > form div > button.train_Search.btnDefault[type='submit']");
    if (submitButton) {
        submitButton.click();
    }
}

// Review booking
async function reviewBooking() {
    console.log('Reviewing booking...');
    
    // Handle captcha
    if (user_data.other_preferences.autoCaptcha) {
        await addDelay(500);
        await handleCaptcha();
    } else {
        console.log('Manual captcha filling');
        const captchaInput = document.querySelector("#captcha");
        if (captchaInput) {
            captchaInput.focus();
        }
    }
    
    // If auto submit is enabled, click the continue button
    if (user_data.other_preferences.CaptchaSubmitMode === 'A') {
        await addDelay(2000);
        const continueButton = document.querySelector(".btnDefault.train_Search");
        if (continueButton) {
            // Check if confirm berths only is enabled
            if (user_data.other_preferences.confirmberths) {
                const availableSeats = document.querySelector(".AVAILABLE");
                if (availableSeats) {
                    console.log('Seats available, continuing...');
                    continueButton.click();
                } else {
                    console.log('No seats available, stopping...');
                    if (confirm('No seats Available, Do you still want to continue booking?')) {
                        continueButton.click();
                    }
                }
            } else {
                continueButton.click();
            }
        }
    }
}

// Handle payment options
async function bkgPaymentOptions() {
    console.log('Handling payment options...');
    
    await addDelay(1000);
    
    let paymentCategory = "Multiple Payment Service";
    let paymentOption = "IRCTC iPay (Credit Card/Debit Card/UPI)";
    
    // Set payment method based on user preference
    if (user_data.other_preferences.paymentmethod.includes("IRCUPI")) {
        paymentCategory = "IRCTC iPay (Credit Card/Debit Card/UPI)";
        paymentOption = "Credit cards/Debit cards/Netbanking/UPI (Powered by IRCTC)";
        console.log('Payment option: IRCUPI');
    } else if (user_data.other_preferences.paymentmethod.includes("PAYTMUPI")) {
        paymentCategory = "BHIM/ UPI/ USSD";
        paymentOption = "Pay using BHIM (Powered by PAYTM ) also accepts UPI";
        console.log('Payment option: PAYTMUPI');
    } else if (user_data.other_preferences.paymentmethod.includes("HDFCDB")) {
        paymentCategory = "Payment Gateway / Credit Card / Debit Card";
        paymentOption = "Visa/Master Card(Powered By HDFC BANK)";
        console.log('Payment option: HDFCDB');
    }
    
    // Handle special characters in payment option
    let paymentOptionEscaped = paymentOption.replace("&", "&amp;");
    
    // Wait for payment options to load
    await waitForElement(".bank-type", 20);
    
    // Select payment category
    const paymentCategories = document.getElementById("pay-type").getElementsByTagName("div");
    for (let i = 0; i < paymentCategories.length; i++) {
        if (paymentCategories[i].innerText.indexOf(paymentCategory) >= 0) {
            paymentCategories[i].click();
            await addDelay(500);
            
            // Select payment option
            const paymentOptions = document.getElementsByClassName("border-all no-pad");
            let optionFound = false;
            
            for (let j = 0; j < paymentOptions.length; j++) {
                if (paymentOptions[j].getBoundingClientRect().top != 0 && 
                    paymentOptions[j].getElementsByTagName("span")[0].innerHTML.toUpperCase().indexOf(paymentOptionEscaped.toUpperCase()) != -1) {
                    
                    paymentOptions[j].click();
                    optionFound = true;
                    
                    // Click continue button
                    await addDelay(500);
                    const continueButton = document.getElementsByClassName("btn-primary")[0];
                    if (continueButton) {
                        continueButton.click();
                    }
                    
                    break;
                }
            }
            
            if (!optionFound) {
                alert("Selected payment option not available, please select other option manually.");
            }
            
            break;
        }
    }
}

// Helper function to wait for an element to appear
async function waitForElement(selector, maxWaitSeconds, parent = document) {
    return new Promise((resolve, reject) => {
        if (parent.querySelector(selector)) {
            return resolve(parent.querySelector(selector));
        }
        
        const observer = new MutationObserver(() => {
            if (parent.querySelector(selector)) {
                observer.disconnect();
                resolve(parent.querySelector(selector));
            }
        });
        
        observer.observe(parent, {
            childList: true,
            subtree: true
        });
        
        setTimeout(() => {
            observer.disconnect();
            resolve(null);
        }, maxWaitSeconds * 1000);
    });
}

// Initialize when the page loads
window.onload = function() {
    // Set up a periodic status update
    setInterval(function() {
        console.log('Extension is active');
    }, 15000);
    
    // Get booking data from storage
    chrome.storage.local.get([
        'irctc_credentials',
        'journey_details',
        'passenger_details',
        'infant_details',
        'other_preferences',
        'travel_preferences'
    ], function(data) {
        if (data.irctc_credentials && data.journey_details && data.passenger_details) {
            user_data = data;
            startBookingProcess();
        }
    });
};
