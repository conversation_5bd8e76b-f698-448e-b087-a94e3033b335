/* Static Pages Styles */
.static-page {
  min-height: calc(100vh - 140px); /* Adjust for header/footer */
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px 0;
}

.static-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Header */
.static-header {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
  text-align: center;
}

.back-link {
  display: inline-block;
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  margin-bottom: 20px;
  padding: 8px 16px;
  border: 1px solid #667eea;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.back-link:hover {
  background: #667eea;
  color: white;
}

.static-header h1 {
  color: #2d3748;
  margin: 0 0 10px 0;
  font-size: 36px;
  font-weight: 700;
}

.last-updated, .subtitle {
  color: #718096;
  margin: 0;
  font-size: 16px;
}

/* Content */
.static-content {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  line-height: 1.6;
}

.static-content section {
  margin-bottom: 40px;
}

.static-content section:last-child {
  margin-bottom: 0;
}

.static-content h2 {
  color: #2d3748;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e2e8f0;
}

.static-content h3 {
  color: #4a5568;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 12px;
}

.static-content p {
  color: #4a5568;
  margin-bottom: 16px;
}

.static-content ul {
  margin-bottom: 16px;
  padding-left: 20px;
}

.static-content li {
  color: #4a5568;
  margin-bottom: 8px;
}

.static-content strong {
  color: #2d3748;
  font-weight: 600;
}

.contact-info {
  background: #f7fafc;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.contact-info p {
  margin-bottom: 8px;
}

.contact-info a {
  color: #667eea;
  text-decoration: none;
}

.contact-info a:hover {
  text-decoration: underline;
}

/* Support Page Specific Styles */
.support-tabs {
  display: flex;
  background: white;
  border-radius: 12px;
  padding: 8px;
  margin-bottom: 30px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-btn {
  flex: 1;
  padding: 12px 24px;
  border: none;
  background: transparent;
  color: #718096;
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tab-btn.active {
  background: #667eea;
  color: white;
}

.tab-btn:hover:not(.active) {
  background: #f7fafc;
  color: #4a5568;
}

/* FAQ Section */
.faq-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.faq-item {
  background: #f7fafc;
  padding: 24px;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.faq-item h3 {
  color: #2d3748;
  margin-bottom: 12px;
  font-size: 18px;
}

.faq-item p {
  margin-bottom: 0;
  color: #4a5568;
}

/* Contact Section */
.contact-methods {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.contact-method {
  background: #f7fafc;
  padding: 24px;
  border-radius: 8px;
  text-align: center;
}

.contact-method h3 {
  color: #2d3748;
  margin-bottom: 12px;
  font-size: 18px;
}

.contact-method p {
  color: #718096;
  margin-bottom: 8px;
}

.contact-method a {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
}

.contact-method a:hover {
  text-decoration: underline;
}

/* Contact Form */
.contact-form-container {
  background: #f7fafc;
  padding: 30px;
  border-radius: 8px;
}

.contact-form-container h3 {
  margin-bottom: 24px;
  color: #2d3748;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  color: #4a5568;
  font-weight: 500;
  margin-bottom: 8px;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 16px;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.submit-btn {
  background: #667eea;
  color: white;
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
}

.submit-btn:hover {
  background: #5a67d8;
}

.form-success {
  text-align: center;
  padding: 40px;
}

.form-success h4 {
  color: #38a169;
  margin-bottom: 12px;
  font-size: 20px;
}

.form-success p {
  color: #4a5568;
}

/* Guides Section */
.guide-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.guide-card {
  background: #f7fafc;
  padding: 24px;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.guide-card h3 {
  color: #2d3748;
  margin-bottom: 12px;
  font-size: 18px;
}

.guide-card p {
  color: #718096;
  margin-bottom: 16px;
}

.guide-card ul {
  margin-bottom: 0;
}

.guide-card li {
  color: #4a5568;
  margin-bottom: 6px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .static-container {
    padding: 0 16px;
  }

  .static-header {
    padding: 20px;
  }

  .static-header h1 {
    font-size: 28px;
  }

  .static-content {
    padding: 24px;
  }

  .support-tabs {
    flex-direction: column;
  }

  .tab-btn {
    margin-bottom: 4px;
  }

  .contact-methods {
    grid-template-columns: 1fr;
  }

  .contact-form-container {
    padding: 20px;
  }

  .guide-cards {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .static-header h1 {
    font-size: 24px;
  }

  .static-content {
    padding: 16px;
  }

  .static-content h2 {
    font-size: 20px;
  }
}
