document.addEventListener('DOMContentLoaded', function() {
    // Load station list
    fetch(chrome.runtime.getURL('stationlist.json'))
        .then(response => response.json())
        .then(stations => {
            const datalist = document.getElementById('stations-list');
            stations.forEach(station => {
                const option = document.createElement('option');
                option.value = `${station.name} (${station.code})`;
                datalist.appendChild(option);
            });
        });

    // Toggle Tatkal preferences
    document.getElementById('auto-book-tatkal').addEventListener('change', function(e) {
        const tatkalPrefs = document.querySelector('.tatkal-preferences');
        tatkalPrefs.style.display = e.target.checked ? 'block' : 'none';
    });

    // Load saved preferences
    chrome.storage.local.get(['preferences', 'vpa', 'other_preferences', 'stations'], function(result) {
        if (result.preferences) {
            document.getElementById('payment-method').value = result.preferences.paymentMethod || 'PAYTMUPIID';
            document.getElementById('auto-book-tatkal').checked = result.preferences.autoBookTatkal || false;
            document.getElementById('auto-fill-captcha').checked = result.preferences.autoFillCaptcha || false;
            document.getElementById('preferred-class').value = result.preferences.preferredClass || '3A';
            document.getElementById('preferred-passengers').value = result.preferences.preferredPassengers || 1;
        }
        
        if (result.stations) {
            document.getElementById('source-station').value = result.stations.source || '';
            document.getElementById('destination-station').value = result.stations.destination || '';
        }
        
        if (result.vpa) {
            document.getElementById('vpa').value = result.vpa.vpa || '';
        }

        if (result.other_preferences) {
            document.getElementById('card-number').value = result.other_preferences.cardnumber || '';
            document.getElementById('card-expiry').value = result.other_preferences.cardexpiry || '';
            document.getElementById('static-password').value = result.other_preferences.staticpassword || '';
        }

        // Show/hide Tatkal preferences based on saved state
        document.querySelector('.tatkal-preferences').style.display = 
            (result.preferences && result.preferences.autoBookTatkal) ? 'block' : 'none';
    });

    // Handle form submission
    document.getElementById('preferences-form').addEventListener('submit', function(e) {
        e.preventDefault();

        const preferences = {
            paymentMethod: document.getElementById('payment-method').value,
            autoBookTatkal: document.getElementById('auto-book-tatkal').checked,
            autoFillCaptcha: document.getElementById('auto-fill-captcha').checked,
            preferredClass: document.getElementById('preferred-class').value,
            preferredPassengers: document.getElementById('preferred-passengers').value
        };

        const stations = {
            source: document.getElementById('source-station').value,
            destination: document.getElementById('destination-station').value
        };

        const vpa = {
            vpa: document.getElementById('vpa').value
        };

        const other_preferences = {
            cardnumber: document.getElementById('card-number').value,
            cardexpiry: document.getElementById('card-expiry').value,
            staticpassword: document.getElementById('static-password').value,
            paymentmethod: document.getElementById('payment-method').value
        };

        // Validate stations
        if (!validateStation(stations.source) || !validateStation(stations.destination)) {
            alert('Please select valid stations from the list');
            return;
        }

        // Save to Chrome storage
        chrome.storage.local.set({
            preferences: preferences,
            stations: stations,
            vpa: vpa,
            other_preferences: other_preferences
        }, function() {
            alert('Preferences saved successfully!');
        });
    });

    // Station validation helper
    function validateStation(stationString) {
        return stationString && stationString.match(/^.+\s\([A-Z]+\)$/);
    }
});
